import 'package:flutter/material.dart';
import 'package:vp_bond/core/common/widget/bond_none_widget.dart';
import 'package:vp_bond/generated/assets.gen.dart';
import 'package:vp_bond/generated/l10n.dart';
import 'package:vp_bond/screen/referral_order/bloc/referral_order_cubit.dart';
import 'package:vp_bond/screen/referral_order/widget/list_referral_order_non_concentrated.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class ReferralOrderScreen extends StatefulWidget {
  const ReferralOrderScreen({super.key});

  @override
  State<ReferralOrderScreen> createState() => _ReferralOrderScreenState();
}

class _ReferralOrderScreenState extends State<ReferralOrderScreen>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider(
      create: (context) => ReferralOrderCubit()..fetchReferralHistory(),
      child: VPScaffold(
        backgroundColor: themeData.bgMain,
        extendBody: true,
        body: Column(
          children: [
            HeaderWidget(
              subTitle: "Trái phiếu",
              title: S.current.referralOrder,
              actionBack: () {
                context.pop();
              },
            ),
            Expanded(
              child: DefaultTabController(
                initialIndex: 1,
                length: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    VPTabBar(
                      labelStyle: vpTextStyle.body14?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                      unselectedLabelStyle: vpTextStyle.body14?.copyWith(
                        color: vpColor.textSecondary,
                      ),
                      tabs: [
                        Tab(text: S.current.listed),
                        Tab(text: S.current.unListed),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [
                          BondNoneWidget(
                            height: double.infinity,
                            physics: const AlwaysScrollableScrollPhysics(),
                            image: Assets.images.imEmpty.path,
                            desc: S.current.noneOfRecommendOrder,
                          ),

                          ListReferralOrderNonConcentrated(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
