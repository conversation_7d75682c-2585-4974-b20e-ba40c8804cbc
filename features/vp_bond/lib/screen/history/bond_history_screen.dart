import 'package:flutter/material.dart';
import 'package:vp_bond/router/vp_bond_router.dart';
import 'package:vp_bond/screen/history/bloc/bond_history_list/bond_history_list_cubit.dart';
import 'package:vp_bond/screen/history/widget/bond_list_history.dart';
import 'package:vp_bond/screen/history/widget/bond_list_non_concentrated.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class BondHistoryScreen extends StatefulWidget {
  const BondHistoryScreen({super.key});

  @override
  State<BondHistoryScreen> createState() => _BondHistoryScreenState();
}

class _BondHistoryScreenState extends State<BondHistoryScreen>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider(
      create: (context) => BondHistoryListCubit()..fetchBondHistory(),
      child: VPScaffold(
        backgroundColor: themeData.bgMain,
        body: Column(
          children: [
            HeaderWidget(
              subTitle: "<PERSON>r<PERSON><PERSON> phiếu",
              title: "<PERSON><PERSON>ch sử giao dịch",
              actionBack: () {
                if (context.canPop()) {
                  context.pop();
                } else {
                  context.go(VPBondRouterPaths.bondManagerPage);
                }
              },
            ),
            Expanded(
              child: DefaultTabController(
                length: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    VPTabBar(
                      labelStyle: vpTextStyle.body14?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                      unselectedLabelStyle: vpTextStyle.body14?.copyWith(
                        color: vpColor.textSecondary,
                      ),
                      tabs: [
                        Tab(text: 'TPRL GD tập trung'),
                        Tab(text: 'TPRL GD không tập trung'),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [
                          const BondHistoryList(),
                          BondListNonConcentrated(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
