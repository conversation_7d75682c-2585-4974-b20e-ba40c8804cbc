//import 'dart:developer' as developer;

import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:vp_bond/core/common/utils/bond_utils.dart';
import 'package:vp_bond/core/common/widget/bond_bottom_sheet.dart';
import 'package:vp_bond/core/common/widget/bond_button_widget.dart';
import 'package:vp_bond/core/common/widget/bond_loading_widget.dart';
import 'package:vp_bond/core/common/widget/bond_warning_dialog.dart';
import 'package:vp_bond/core/common/widget/button/bond_button_widget.dart';
import 'package:vp_bond/core/common/widget/constains.dart';
import 'package:vp_bond/core/common/widget/warning_dialog.dart';
import 'package:vp_bond/generated/assets.gen.dart';
import 'package:vp_bond/generated/l10n.dart';
import 'package:vp_bond/model/response/list_offer/oxpost_group_model.dart';
import 'package:vp_bond/router/vp_bond_router.dart';
import 'package:vp_bond/screen/detail/widgets/bond_report_widget.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

import '../bloc/bond_detail_bloc.dart';
import '../bloc/bond_detail_state.dart';
import '../widgets/bond_detail_info_widget.dart';
import '../widgets/period_info_widget.dart';

class BondDetailOldArgument {
  OxpostGroupModel? bondModel;
  String? symbol;
  String? ndtcn;
  String? ndtcnEn;
  String? maxAmt;
  String? minAmt;
  String? rate;
  List<String>? listTerm;
  List<String>? listRate;
  String minMaxInvest;

  BondDetailOldArgument({
    this.minMaxInvest = '',
    this.maxAmt,
    this.minAmt,
    this.symbol,
    this.ndtcn,
    this.ndtcnEn,
    this.rate,
    this.listTerm,
    this.listRate,
    this.bondModel,
  }) {
    final min = int.tryParse(minAmt ?? '0') ?? 0;
    final max = int.tryParse(maxAmt ?? '0') ?? 0;
    minMaxInvest = '${min.toString()}/${max.toString()}';
  }
}

class BondDetailOldPage extends StatefulWidget {
  final BondDetailOldArgument argument;

  const BondDetailOldPage({super.key, required this.argument});

  @override
  BondDetailOldPageState createState() => BondDetailOldPageState();
}

class BondDetailOldPageState extends State<BondDetailOldPage> {
  late final BondDetailBloc _bloc;

  String getIntPaidFrq(String value) {
    String cktl = value;
    switch (value) {
      case 'L':
        cktl = S.current.endOfTerm;
        break;
      case 'Y':
        cktl = S.current.annual;
        break;
      case 'H':
        cktl = S.current.halfOfYear;
        break;
      case 'Q':
        cktl = S.current.quarterly;
        break;
      case 'M':
        cktl = S.current.monthly;
        break;
    }
    return cktl;
  }

  String get symbol => widget.argument.symbol ?? '';
  String get disbursementCode => widget.argument.symbol ?? '';
  bool get requireProfessionalInvestor => widget.argument.ndtcnEn == 'Yes';

  @override
  void initState() {
    super.initState();
    _bloc = BondDetailBloc()..getBondInfoOld(symbol);
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      header: AppBarLayer2Widget(
        subTitle1: S.current.bondInfo,
        subTitle2: widget.argument.symbol ?? '',
        // leading: const BondBackButton(
        //   screenName: BondRouter.bondDetailPage,
        // ),
      ),
      bottomBar: Container(
        height: 110,
        decoration: BoxDecoration(
          border: Border(top: BorderSide(width: 1, color: themeData.divider)),
        ),
        padding: EdgeInsets.fromLTRB(
          8,
          16,
          8,
          MediaQuery.of(context).padding.bottom + 16,
        ),
        child: BondButtonWidget(
          onPressed: () {
            if (widget.argument.ndtcnEn == 'Yes' &&
                !checkIsProfessionCustomer()) {
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) {
                  return WarningDialog(
                    title:
                        S
                            .current
                            .notification, // getBondLang(BondKeyLang.notification),
                    content: S.current.bondProfessionalRequire, //
                    asset: Assets.icons.icWarning.svg(),
                    action: [
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                          context.push(
                            VPBondRouterPaths.bondOrderProInvestorInfoPage,
                          );
                        },
                        child: Text(
                          S
                              .current
                              .findOutMore, //   getBondLang(BondKeyLang.findOutMore),
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium!.copyWith(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      kSpacer,
                      BondButtonWidget(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        action:
                            S.current.agree, // getBondLang(BondKeyLang.agree),
                      ),
                    ],
                  );
                },
              );
            } else {
              if (widget.argument.bondModel != null) {
                context.push(
                  VPBondRouterPaths.bondOrderPage,
                  extra: widget.argument.bondModel,
                );
              } else {
                Navigator.of(context).pop();
              }
            }
          },
          action: S.current.order,
        ),
      ),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: BlocListener<BondDetailBloc, BondDetailState>(
          bloc: _bloc,
          listener: (_, state) {
            state.when(
              oldData: (data) {},
              (data) {},
              loading: () {},
              error: (error) {
                showDialog(
                  context: context,
                  useRootNavigator: true,
                  builder: (_) {
                    return BondWarningDialog(
                      title: S.current.noData,
                      asset: Assets.icons.icWarning.svg(),
                      content: S.current.somethingError,
                      action: [
                        Expanded(
                          child: BondButtonWidgetOld(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            title: S.current.close,
                            titleColor: themeData.gray700,
                            borderColor: themeData.gray700,
                            color: themeData.white,
                          ),
                        ),
                      ],
                    );
                  },
                );
              },
            );
          },
          child: BlocBuilder<BondDetailBloc, BondDetailState>(
            bloc: _bloc,
            builder: (_, state) {
              return state.when(
                (data) {
                  return const SizedBox();
                },
                oldData: (data) {
                  return ListView(
                    shrinkWrap: true,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          kSpacingHeight16,
                          Text(
                            S.current.infoOverview,

                            style: Theme.of(context).textTheme.titleSmall,
                          ),
                          kSpacingHeight8,
                          if (data.symbol != null)
                            BondDetailInfoWidget(
                              title:
                                  S
                                      .current
                                      .bondCode, // getBondLang(BondKeyLang.bondCode),
                              content: data.symbol,
                            ),
                          if (data.tcph != null)
                            BondDetailInfoWidget(
                              title:
                                  S
                                      .current
                                      .issuer, // getBondLang(BondKeyLang.issuer),
                              content: data.tcph,
                            ),
                          if (data.opndate != null)
                            BondDetailInfoWidget(
                              title:
                                  S
                                      .current
                                      .releaseDate, // getBondLang(BondKeyLang.releaseDate),
                              content: data.opndate,
                            ),
                          if (data.duedate != null)
                            BondDetailInfoWidget(
                              title:
                                  S
                                      .current
                                      .dateDue, // getBondLang(BondKeyLang.dateDue),
                              content: data.duedate,
                            ),
                          // BondDetailInfoWidget(
                          //     title: 'Loại lãi suất', content: '???????'),
                          if ((widget.argument.rate ?? '').trim().isNotEmpty)
                            BondDetailInfoWidget(
                              title:
                                  S
                                      .current
                                      .couponInterest, // getBondLang(BondKeyLang.couponInterest),
                              content: '${widget.argument.rate}%',
                            ),
                          if (data.intpaidfrq != null)
                            BondDetailInfoWidget(
                              title:
                                  S
                                      .current
                                      .couponPeriod, // getBondLang(BondKeyLang.couponPeriod),
                              content: getIntPaidFrq(data.intpaidfrq!),
                            ),
                          if (data.assetbackeddesc != null)
                            BondDetailInfoWidget(
                              title:
                                  S
                                      .current
                                      .collateral, // getBondLang(BondKeyLang.collateral),
                              content: data.assetbackeddesc,
                            ),
                          if ((widget.argument.ndtcn ?? '').isNotEmpty)
                            BondDetailInfoWidget(
                              title:
                                  S.current.professionalRequire, // getBondLang(

                              content: widget.argument.ndtcn,
                            ),
                        ],
                      ),
                      if (_showListTerm())
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            kSpacingHeight8,
                            Text(
                              S
                                  .current
                                  .interestRateChart, //    getBondLang(BondKeyLang.interestRateChart),
                              style: Theme.of(context).textTheme.titleSmall,
                            ),
                            kSpacingHeight8,
                            Row(
                              children: [
                                Text(
                                  S
                                      .current
                                      .liquidityPackages, //   getBondLang(BondKeyLang.liquidityPackages),
                                  style: Theme.of(context).textTheme.bodySmall!
                                      .copyWith(color: themeData.gray500),
                                ),
                                kSpacer,
                                Text(
                                  '${S.current.interest}/${S.current.year}',
                                  style: Theme.of(context).textTheme.bodySmall!
                                      .copyWith(color: themeData.gray500),
                                ),
                              ],
                            ),
                            if (_showListTerm())
                              ListView.separated(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemBuilder:
                                    (_, index) => PeriodInfoWidget(
                                      period: widget.argument.listTerm![index],
                                      rate: widget.argument.listRate![index],
                                    ),
                                separatorBuilder:
                                    (_, index) => Divider(
                                      thickness: 1,
                                      height: 1,
                                      color: themeData.gray100,
                                    ),
                                itemCount: widget.argument.listTerm!.length,
                              ),
                            if (widget.argument.listTerm != null &&
                                widget.argument.listTerm!.isNotEmpty)
                              Divider(
                                thickness: 1,
                                height: 1,
                                color: themeData.gray100,
                              ),
                          ],
                        ),
                      if (data.refurl != null || data.financerpturl != null)
                        kSpacingHeight16,
                      if (data.refurl != null || data.financerpturl != null)
                        Text(
                          S.current.attachFile, //

                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                      if (data.financerpturl != null) kSpacingHeight8,
                      if (data.financerpturl != null)
                        Row(
                          children: [
                            InkWell(
                              onTap: () {
                                _showPreviewReportWidget(data.financerpturl);
                              },
                              child: Text(
                                S
                                    .current
                                    .financialStatement, //  getBondLang(BondKeyLang.financialStatement),
                                style: Theme.of(
                                  context,
                                ).textTheme.titleMedium!.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            ),
                            kSpacingWidth8,
                            InkResponse(
                              radius: 16,
                              onTap: () {
                                _showPreviewReportWidget(data.financerpturl);
                              },
                              child: Assets.icons.icDownload.svg(),
                              //  SvgPicture.asset(BondKeyAssets.icDownload),
                            ),
                          ],
                        ),
                      if (data.refurl != null) kSpacingHeight8,
                      if (data.refurl != null)
                        Row(
                          children: [
                            InkWell(
                              onTap: () {
                                _showPreviewReportWidget(data.refurl);
                              },
                              child: Text(
                                S
                                    .current
                                    .prospectus, //  getBondLang(BondKeyLang.prospectus),
                                style: vpTextStyle.subtitle14?.copyWith(
                                  color: vpColor.textBrand,
                                ),
                              ),
                            ),
                            kSpacingWidth8,
                            InkResponse(
                              radius: 16,
                              onTap: () {
                                _showPreviewReportWidget(data.refurl);
                              },
                              child: Assets.icons.icDownload.svg(),
                              // SvgPicture.asset(BondKeyAssets.icDownload),
                            ),
                          ],
                        ),
                      kSpacingHeight16,
                    ],
                  );
                },
                loading: () => const BondLoadingWidget(),
                error: (err) => const SizedBox.shrink(),
              );
            },
          ),
        ),
      ),
    );
  }

  void _showPreviewReportWidget(String? reportName) {
    showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return BondBottomSheet(
          initialChildSize:
              MediaQuery.of(context).size.height -
              MediaQueryData.fromView(window).padding.top -
              kToolbarHeight,
          isFullSize: true,
          child: BondReportWidget(
            title:
                S
                    .current
                    .registrationBuyBond, // getBondLang(BondKeyLang.registrationBuyBond),
            reportName: reportName,
            symbol: widget.argument.symbol,
          ),
        );
      },
    );
  }

  bool _showListTerm() {
    return widget.argument.bondModel?.productDetail != null &&
        widget.argument.bondModel!.productDetail!.any(
          (element) => !element.isOutRight(),
        ) &&
        widget.argument.listTerm != null &&
        widget.argument.listTerm!.isNotEmpty;
  }
}
