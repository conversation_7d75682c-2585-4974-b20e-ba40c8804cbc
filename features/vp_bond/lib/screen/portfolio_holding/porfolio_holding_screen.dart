import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_bond/core/constant/constant_app.dart';
import 'package:vp_bond/generated/assets.gen.dart';
import 'package:vp_bond/router/vp_bond_router.dart';
import 'package:vp_bond/screen/portfolio_holding/bloc/list_porfolio_holding/bond_portfolio_bloc.dart';
import 'package:vp_bond/screen/portfolio_holding/widget/list_porfolio_holding.dart';
import 'package:vp_bond/screen/portfolio_holding/widget/list_portfolio_non_concentrated.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class PorfolioHoldingScreen extends StatefulWidget {
  const PorfolioHoldingScreen({super.key});

  @override
  State<PorfolioHoldingScreen> createState() => _PorfolioHoldingScreenState();
}

class _PorfolioHoldingScreenState extends State<PorfolioHoldingScreen>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final colorUtils = Theme.of(context);
    return BlocProvider(
      create: (context) => BondPortfolioCubit()..fetchPortfolio(),
      child: VPScaffold(
        backgroundColor: colorUtils.bgMain,
        extendBody: true,
        body: Column(
          children: [
            HeaderWidget(
              subTitle: "Trái phiếu",
              title: "Danh mục nắm giữ",
              actionBack: () {
                context.pop();
              },
              actionRight: () {},
              icon: Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: InkResponse(
                  onTap: () {
                    context.push(VPBondRouterPaths.bondPortfolioContractPage);
                  },
                  radius: 24,
                  child: SvgPicture.asset(
                    Assets.icons.icBondPortfolio.path,
                    package: vpBond,
                    colorFilter: ColorFilter.mode(
                      colorUtils.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: DefaultTabController(
                length: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    VPTabBar(
                      labelStyle: vpTextStyle.body14?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                      unselectedLabelStyle: vpTextStyle.body14?.copyWith(
                        color: vpColor.textSecondary,
                      ),
                      tabs: [
                        Tab(text: 'TPRL GD tập trung'),
                        Tab(text: 'TPRL GD không tập trung'),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [
                          ListPorfolioHolding(),
                          ListPorfolioNonConcentrated(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
