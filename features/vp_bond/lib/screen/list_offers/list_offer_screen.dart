import 'package:flutter/material.dart';
import 'package:vp_bond/core/common/utils/bond_utils.dart';
import 'package:vp_bond/core/common/widget/bond_bottom_sheet.dart';
import 'package:vp_bond/generated/assets.gen.dart';
import 'package:vp_bond/screen/list_offers/bloc/list_offers_cubit.dart';
import 'package:vp_bond/screen/list_offers/widget/info_bottom_sheet.dart';
import 'package:vp_bond/screen/list_offers/widget/list_offer.dart';
import 'package:vp_bond/screen/list_offers/widget/list_offer_non_concentrated.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class ListOfferScreen extends StatefulWidget {
  const ListOfferScreen({super.key});

  @override
  State<ListOfferScreen> createState() => _ListOfferScreenState();
}

class _ListOfferScreenState extends State<ListOfferScreen>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      setInfoCustomer();
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final colorUtils = Theme.of(context);
    return BlocProvider(
      create: (context) => ListOffersCubit()..fetchBondProducts(),
      child: VPScaffold(
        backgroundColor: colorUtils.bgMain,
        extendBody: true,
        body: Column(
          children: [
            HeaderWidget(
              subTitle: "Trái phiếu",
              title: "Danh sách chào bán",
              actionBack: () {
                context.pop();
              },
              actionRight: () {},
              icon: Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: InkResponse(
                  onTap: () {
                    showModalBottomSheet(
                      barrierColor: colorUtils.overlayBottomSheet,
                      context: context,
                      useRootNavigator: true,
                      isScrollControlled: true,
                      builder: (cxt) {
                        return const BondBottomSheet(
                          isFullSize: true,
                          child: InfoBottomSheet(),
                        );
                      },
                    );
                  },
                  radius: 24,
                  child: Assets.icons.icInfoV3.svg(
                    colorFilter: ColorFilter.mode(
                      colorUtils.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: DefaultTabController(
                length: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    VPTabBar(
                      labelStyle: vpTextStyle.body14?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                      unselectedLabelStyle: vpTextStyle.body14?.copyWith(
                        color: vpColor.textSecondary,
                      ),
                      tabs: [
                        Tab(text: 'TPRL GD tập trung'),
                        Tab(text: 'TPRL GD không tập trung'),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [ListOffer(), ListOfferNonConcentrated()],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
