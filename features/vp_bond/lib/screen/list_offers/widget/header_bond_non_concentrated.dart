//import 'dart:developer' as developer;
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:vp_bond/core/common/utils/bond_utils.dart';
import 'package:vp_bond/generated/assets.gen.dart';
import 'package:vp_bond/generated/l10n.dart';
import 'package:vp_bond/model/response/list_offer/oxpost_group_model.dart';
import 'package:vp_common/vp_common.dart' hide Assets;
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';

class HeaderBondNonConcentrated extends StatelessWidget {
  final OxpostGroupModel data;
  final bool? isExpanded;

  const HeaderBondNonConcentrated({
    super.key,
    required this.data,
    this.isExpanded,
  });

  @override
  Widget build(BuildContext context) {
    ProductDetail? bondLimit;
    ProductDetail? bondOutright;
    data.productDetail?.forEach((bond) {
      if (bond.isOutRight()) {
        bondOutright = bond;
      } else {
        bondLimit = bond;
      }
    });
    ProductDetail? bondData = bondLimit ?? bondOutright;
    double totalLimit =
        (bondLimit?.limitAmountDouble ?? 0) +
        (bondOutright?.limitAmountDouble ?? 0);
    final colorUtils = Theme.of(context);
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16),
      color:
          isExpanded != null
              ? (isExpanded! ? colorUtils.highlightBg : colorUtils.bgMain)
              : null,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(height: 16),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Flexible(
                          child: Text(
                            data.symbol?.replaceAll("", "\u{200B}") ?? '',
                            style: vpTextStyle.subtitle14.copyColor(
                              colorUtils.gray900,
                            ),

                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        // const SizedBox(width: 7.5),
                        // SvgPicture.asset(BondKeyAssets.icFire),
                        if ((bondLimit?.isProfessionRequire() ?? false) ||
                            (bondOutright?.isProfessionRequire() ?? false))
                          SizedBox(width: 8),
                        if ((bondLimit?.isProfessionRequire() ?? false) ||
                            (bondOutright?.isProfessionRequire() ?? false))
                          Assets.icons.icPro.svg(),
                        const SizedBox(width: 20.5),
                      ],
                    ),
                    if (bondData?.timeRemaining != null) SizedBox(width: 4),
                    Text(
                      '${bondData!.timeRemaining} ${S.current.month.toLowerCase()}',
                      style: vpTextStyle.captionRegular.copyColor(
                        colorUtils.gray500,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                flex: 4,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Flexible(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (totalLimit > 0)
                            Text(
                              totalLimit.getMillionUpperUnit(
                                billion: S.current.billion.toLowerCase(),
                                million: S.current.million.toLowerCase(),
                              ),
                              style: vpTextStyle.subtitle14.copyColor(
                                colorUtils.gray900,
                              ),
                            )
                          else
                            SizedBox(height: 24),
                          SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              if (bondData.listterm != null)
                                Flexible(
                                  child: Text(
                                    [
                                          bondLimit != null
                                              ? ('${getMinMaxTermString(bondData.listterm)} ${S.current.month}')
                                              : null,
                                          bondOutright != null ? 'ĐĐH' : null,
                                        ]
                                        .where(
                                          (element) =>
                                              element?.isNotEmpty ?? false,
                                        )
                                        .join(', '),
                                    style: vpTextStyle.captionRegular.copyColor(
                                      colorUtils.gray500,
                                    ),

                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 16),
                    if (bondData.maxrate != null)
                      Column(
                        children: [
                          Text(
                            S.current.max,
                            style: Theme.of(context).textTheme.bodySmall!
                                .copyWith(color: colorUtils.gray500),
                          ),
                          Text(
                            '${max((double.tryParse(bondOutright?.maxrate ?? '') ?? 0), (double.tryParse(bondLimit?.maxrate ?? '') ?? 0))}%',
                            style: vpTextStyle.headine6?.copyWith(
                              color: colorUtils.primary,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
        ],
      ),
    );
  }
}
