import 'package:flutter/material.dart';
import 'package:vp_bond/core/common/widget/button/bond_button_widget.dart';
import 'package:vp_bond/core/common/widget/warning_dialog.dart';
import 'package:vp_bond/generated/assets.gen.dart';
import 'package:vp_bond/generated/l10n.dart';
import 'package:vp_bond/model/response/list_offer/oxpost_group_model.dart';
import 'package:vp_bond/router/vp_bond_router.dart';
import 'package:vp_bond/screen/detail/bond_detail_old/bond_detail_old_page.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class BottomBondNonConcentratedextends extends StatelessWidget {
  final OxpostGroupModel data;
  final bool colorBg;

  const BottomBondNonConcentratedextends({
    super.key,
    required this.data,
    this.colorBg = false,
  });

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return Container(
      color: colorBg ? colorUtils.highlightBg : null,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(width: 16),
              InkWell(
                onTap: () {
                  // BondTracking.instance
                  //     .logEvent(name: EventTracking.bondHomeBondInfoClick);
                  context.push(
                    VPBondRouterPaths.bondDetailPage,
                    extra: BondDetailOldArgument(
                      bondModel: data,
                      symbol: data.symbol,
                      ndtcn: data.productDetail!.first.isgioihanndtdesc,
                      ndtcnEn: data.productDetail!.first.isgioihanndtdescen,
                      rate: data.productDetail!.first.intrate,
                      minAmt: data.productDetail!.first.minamt,
                      maxAmt: data.productDetail!.first.maxamt,
                      listTerm:
                          data.productDetail
                              ?.firstWhere(
                                (bond) => !bond.isOutRight(),
                                orElse: () => ProductDetail(),
                              )
                              .listterm,
                      listRate:
                          data.productDetail
                              ?.firstWhere(
                                (bond) => !bond.isOutRight(),
                                orElse: () => ProductDetail(),
                              )
                              .listrate,
                    ),
                  );
                },
                child: Text(
                  S.current.bondInfo,
                  style: vpTextStyle.subtitle14?.copyWith(
                    color: vpColor.textBrand,
                  ),

                  // Theme.of(context).textTheme.titleMedium!.copyWith(
                  //   color: Theme.of(context).primary,
                  //   fontWeight: FontWeight.w600,
                  // ),
                ),
              ),
              Spacer(),
              BondButtonWidget(
                onPressed: () {
                  // BondTracking.instance
                  //     .logEvent(name: EventTracking.bondHomeBuyClick);
                  if (_isProfessionRequired()) {
                    showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (context) {
                        return WarningDialog(
                          title: S.current.notification,
                          content:
                              S.current.bondProfessionalRequire, // getBondLang(
                          asset: Assets.icons.icWarning.svg(),
                          action: [
                            InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                                context.push(
                                  VPBondRouterPaths
                                      .bondOrderProInvestorInfoPage,
                                );
                              },
                              child: Text(
                                S
                                    .current
                                    .findOutMore, //  getBondLang(BondKeyLang.findOutMore),
                                style: vpTextStyle.body14.copyColor(
                                  vpColor.textBrand,
                                ),
                              ),
                            ),
                            Spacer(),
                            BondButtonWidget(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              action: S.current.agree,
                            ),
                          ],
                        );
                      },
                    );
                  } else {
                    context.push(VPBondRouterPaths.bondOrderPage, extra: data);
                  }
                },
                action: S.current.order,
              ),
              SizedBox(width: 16),
            ],
          ),
          SizedBox(height: 16),
        ],
      ),
    );
  }

  bool _isProfessionRequired() {
    return data.productDetail!.first.isProfessionRequire();
  }
}
