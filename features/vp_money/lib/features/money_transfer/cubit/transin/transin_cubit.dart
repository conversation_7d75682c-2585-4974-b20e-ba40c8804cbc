import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/cubit/sub_account_cubit.dart';
import 'package:vp_core/model/sign_in_model/sub_account_model.dart';
import 'package:vp_money/core/repository/money_repository.dart';
import 'package:vp_money/generated/l10n.dart';
import 'package:vp_money/model/request/money_transfer/money_transfer_in_request.dart';
import 'package:vp_money/model/response/money_transfer/money_account_recived_obj.dart';
import 'package:vp_money/model/response/money_transfer/money_available_cash_model.dart';

part 'transin_cubit.freezed.dart';
part 'transin_state.dart';

class TransinCubit extends Cubit<TransInState> {
  TransinCubit() : super(TransInState());
  final MoneyRepository _repository = GetIt.instance<MoneyRepository>();

  void init() {
    final subAccounts = GetIt.instance<SubAccountCubit>().defaultSubAccount;
    selectTransferAccount(subAccounts);
  }

  void selectTransferAccount(SubAccountModel? transfer) {
    if (transfer != null && transfer.id != state.transfer?.id) {
      _handleSetValueAfterChangeSubAccount(transfer);
      getInfo();
    }
  }

  void _handleSetValueAfterChangeSubAccount(SubAccountModel? transfer) {
    if (transfer != null && transfer.id != state.transfer?.id) {
      emit(TransInState(transfer: transfer));
    }
  }

  void getInfo() async {
    emit(state.copyWith(isLoading: true));
    try {
      //lấy danh sách tiểu khoản nhận
      var getTransferAccountList = _repository.transferAccountList(
        state.transfer?.id ?? "0",
        MoneyTransferType.internal.name,
      );
      //lấy số dư tối đa
      var getCiInfo = _repository.availableCashTransferOnline(
        state.transfer?.id ?? "0",
      );
      var result = await Future.wait([getTransferAccountList, getCiInfo]);

      num? maxMoney =
          (result[1] as MoneyAvailableCashModel).availableTransferOnline;
      var listAccountReceive = result[0] as List<TransferAccountModel> ?? [];

      emit(
        state.copyWith(
          isLoading: false,
          maxAmount: maxMoney,
          listAccountReceive: listAccountReceive,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
    }
  }

  // Future<num?> getUserMoneyBond() async {
  //   try {
  //     final data = await _repository.getBondBalance();
  //     return data.amount;
  //   } catch (e) {
  //     emit(
  //       state.copyWith(
  //         isLoading: false,
  //         errorMessage: await getErrorMessage(e),
  //       ),
  //     );
  //   }
  //   return null;
  // }

  // Xử lý dropdown khi chọn tiểu khoản nhận
  void selectReceiveAccount(TransferAccountModel? receive) {
    if (receive != null) {
      emit(state.copyWith(receive: receive));
    }
  }

  // Xử lý dropdown khi chọn tiểu khoản chuyển
  void handleSelectTransferAccount(SubAccountModel? subAccount) {
    selectTransferAccount(subAccount);
  }

  void onSelectSuggestMoney(num value) {
    onChangeAmount(value.toFormat3());
  }

  void onChangeAmount(String value) {
    num amount = num.tryParse(value.replaceAll(',', '')) ?? 0;
    emit(state.copyWith(amount: amount));
    String message = '';
    if (amount < 1 && value.isNotEmpty) {
      message = S.current.money_min_value_transfer2;
    } else if (amount > (state.maxAmount ?? 0)) {
      message =
          '${S.current.money_max_cash_input}: ${(state.maxAmount ?? 0).toMoney()}';
    }
    emit(state.copyWith(message: message, amount: amount));
  }

  //*Cập nhật lại request Nội dung chuyển tiền
  void updateContentTransferRequest(String description) {
    var transferRequestObj = state.transferRequestObj.copyWith(
      description: convertText(description),
    );
    emit(state.copyWith(transferRequestObj: transferRequestObj));
  }

  // xử lý call api chuyển tiền nội bộ
  Future<void> handleOnPressTransferMoney() async {
    var obj = state.receive;
    var transferRequestObj = state.transferRequestObj.copyWith(
      accountId: state.transfer?.id ?? '',
      benefitAccountId: obj?.benefitAccountId ?? '',
      amount: state.amount,
      requestId: AppHelper().genXRequestID(),
    );

    try {
      emit(
        state.copyWith(transferRequestObj: transferRequestObj, isLoading: true),
      );
      var result = await _repository.cashInternalTransfer(transferRequestObj);
      if (result?.isSuccess ?? false) {
      } else {}
      emit(state.copyWith(isLoading: false));
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
    }
  }

  String convertText(String text) {
    String regex =
        r'[^\p{Alphabetic}\p{Mark}\p{Decimal_Number}\p{Connector_Punctuation}\p{Join_Control}\s]+';
    return TiengViet.parse(text.replaceAll(RegExp(regex, unicode: true), ''));
  }

  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }
}
