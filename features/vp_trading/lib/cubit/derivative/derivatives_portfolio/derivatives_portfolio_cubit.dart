import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/core/repository/stock_common_repository.dart';
import 'package:vp_stock_common/model/positions/close_position_model.dart';
import 'package:vp_stock_common/model/positions/open_position_model.dart';
import 'package:vp_stock_common/model/positions/position_model.dart';

part 'derivatives_portfolio_state.dart';

class DerivativesPortfolioCubit extends Cubit<DerivativesPortfolioState> {
  DerivativesPortfolioCubit({this.symbol = ''})
    : super(DerivativesPortfolioState(apiStatus: ApiStatus.initial()));

  final _repository = GetIt.instance<StockCommonRepository>();
  final String symbol;

  Future<void> fetchPortfolioData() async {
    if (GetIt.instance<SubAccountCubit>()
            .state
            .derivativeSubAccount
            .firstOrNull ==
        null) {
      emit(
        state.copyWith(
          apiStatus: ApiStatus.done(),
          data: PositionsModel(),
          errorMessage: null,
        ),
      );
      return;
    }

    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));
      var data = await _repository.getOpenPositions(
        accountId:
            GetIt.instance<SubAccountCubit>()
                .state
                .derivativeSubAccount
                .first
                .id ??
            '',
        symbol: symbol,
      );

      emit(
        state.copyWith(
          apiStatus: ApiStatus.done(),
          data: data.data,
          errorMessage: null,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          apiStatus: ApiStatus.error(error),
          errorMessage: await getErrorMessage(error),
        ),
      );
    }
  }

  Future<void> refreshPortfolioData() async {
    await fetchPortfolioData();
  }

  Future<void> updateParameters({
    String? newAccountId,
    String? newSymbol,
  }) async {
    await fetchPortfolioData();
  }
}
