import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_common/error/show_error.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/helper/order_status_socket_mixin.dart';
import 'package:vp_stock_common/model/socket/socket_order_status_data.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';

part 'derivatives_order_book_state.dart';

class DerivativesOrderBookCubit extends Cubit<DerivativesOrderBookState>
    with OrderStatusSocketMixin {
  DerivativesOrderBookCubit() : super(const DerivativesOrderBookState());

  final CommandHistoryRepository _commandHistoryRepository =
      GetIt.instance<CommandHistoryRepository>();

  void init({String? orderStatus}) {
    if (GetIt.instance<SubAccountCubit>()
            .state
            .derivativeSubAccount
            .firstOrNull ==
        null) {
      emit(state.copyWith(listItems: []));
    } else {
      emit(
        state.copyWith(
          request: state.request.copyWith(
            accountId:
                GetIt.instance<SubAccountCubit>()
                    .state
                    .derivativeSubAccount
                    .first
                    .id ??
                "",
            orderStatus: orderStatus,
          ),
        ),
      );
      // Subscribe to WebSocket for order status updates
      subscribeOrderStatusSocket();
      loadData();
    }
  }

  Future<void> loadData() async {
    try {
      emit(state.copyWith(isLoading: true));
      final result = await _commandHistoryRepository.getOrder(
        queries: state.request,
      );
      final items = result.data?.content ?? [];
      emit(state.copyWith(isLoading: false, listItems: items));

      // Update cancel button state after loading data
      updateEnableCancelButton();
    } catch (e) {
      showError(e);
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
      debugPrintStack(stackTrace: StackTrace.current);
    }
  }

  void applyFilter(OrderBookRequest filterRequest) {
    emit(state.copyWith(request: filterRequest));
    loadData();
  }

  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }

  void updateEnableCancelButton() {
    final hasOrdersWithCancelCapability = state.listItems.any(
      (order) => order.isEnableCancel,
    );

    emit(state.copyWith(isEnableCancelButton: hasOrdersWithCancelCapability));
  }

  /// Sets the multi-choice mode state
  void setMultiChoice(bool isMultiChoice) {
    emit(state.copyWith(isMultiChoice: isMultiChoice));
  }

  /// Toggles multi-choice mode
  void toggleMultiChoice() {
    emit(state.copyWith(isMultiChoice: !state.isMultiChoice));
  }

  @override
  void onOrderStatusListener(VPOrderStatusData data) {
    // Reload order data when receiving WebSocket order status updates
    loadData();
  }

  @override
  Future<void> close() {
    // Unsubscribe from WebSocket when cubit is closed
    unsubscribeOrderStatus();
    return super.close();
  }
}
