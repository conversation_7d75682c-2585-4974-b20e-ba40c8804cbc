import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/widget/button/vps_button.dart';
import 'package:vp_stock_common/model/positions/open_position_model.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_portfolio/open_positions/widget/dialog_position_reversal.dart';

class DerivativesPositionItemWidget extends StatelessWidget {
  const DerivativesPositionItemWidget({
    super.key,
    required this.position,
    this.onSetStopLoss,
    this.onViewPendingOrders,
  });

  final OpenPositionModel position;
  final VoidCallback? onSetStopLoss;
  final VoidCallback? onViewPendingOrders;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation0,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: _buildMainContent(context)),
              const SizedBox(width: 16),
              _buildQuantityAndPrice(context),
              const SizedBox(width: 16),
              _buildActionsColumn(context),
            ],
          ),
          const SizedBox(height: 16),
          _buildConditionalUI(context),
          if (position.canSetStopLoss || position.hasPendingTpslOrders)
            const SizedBox(height: 8),
        ],
      ),
    );
  }

  /// Builds the main content section (left column)
  Widget _buildMainContent(BuildContext context) {
    final isLong = position.isLongPosition;
    final positionColor =
        isLong ? vpColor.textPriceGreen : vpColor.textPriceRed;
    final positionText = isLong ? 'L' : 'S';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // Position indicator (L/S)
            Container(
              width: 20,
              height: 24,
              decoration: BoxDecoration(
                color: positionColor.withValues(alpha: 0.16),
                borderRadius: BorderRadius.circular(1),
              ),
              child: Center(
                child: Text(
                  positionText,
                  style: context.textStyle.subtitle14?.copyWith(
                    color: positionColor,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    position.symbol ?? '',
                    style: context.textStyle.subtitle16?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                  Text(
                    (position.totalPlAmt ?? 0).toMoney(showSymbol: false),
                    style: context.textStyle.body14?.copyWith(
                      color: _getPLColor(position.totalPlAmt),
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Builds the quantity and price section (middle column)
  Widget _buildQuantityAndPrice(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '${position.qty ?? 0}/${position.qty ?? 0}',
          style: context.textStyle.captionMedium?.copyWith(
            color: vpColor.textPrimary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          (position.priceSecured ?? 0).toMoney(),
          style: context.textStyle.captionMedium?.copyWith(
            color: vpColor.textPrimary,
          ),
        ),
      ],
    );
  }

  /// Builds the actions column (right column)
  Widget _buildActionsColumn(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Close button
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: vpColor.backgroundElevation0,
            borderRadius: BorderRadius.circular(100),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.06),
                blurRadius: 24,
                offset: const Offset(0, 24),
              ),
            ],
          ),
          child: IconButton(
            onPressed: () {
              if (position.enableButton) {
                // Handle close action
              }
            },
            icon: Icon(
              Icons.close,
              size: 16,
              color:
                  position.enableButton
                      ? vpColor.textPriceRed
                      : vpColor.iconDisabled,
            ),
            padding: EdgeInsets.zero,
          ),
        ),
        const SizedBox(width: 8),
        // Divider
        Container(width: 1, height: 32, color: vpColor.strokeNormal),
        const SizedBox(width: 8),
        // Shuffle button
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: vpColor.backgroundElevation0,
            borderRadius: BorderRadius.circular(100),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.06),
                blurRadius: 24,
                offset: const Offset(0, 24),
              ),
            ],
          ),
          child: IconButton(
            onPressed: () {
              if (position.enableButton) {
                dialogPositionReversal(context, position, () {});
                // Handle close action
              }
            },
            icon: Icon(
              Icons.shuffle,
              size: 16,
              color:
                  position.enableButton
                      ? vpColor.textPriceGreen
                      : vpColor.iconDisabled,
            ),
            padding: EdgeInsets.zero,
          ),
        ),
      ],
    );
  }

  Widget _buildConditionalUI(BuildContext context) {
    if (position.canSetStopLoss) {
      // Show "Cài đặt giá SL/TP" button
      return _buildSetStopLossButton(context);
    } else if (position.hasPendingTpslOrders) {
      // Show pending TPSL order information
      return _buildPendingTpslInfo(context);
    } else {
      // Default case - no conditional UI
      return const SizedBox.shrink();
    }
  }

  /// Builds the "Cài đặt giá SL/TP" button for when isTpsl = "Y"
  Widget _buildSetStopLossButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: VpsButton.secondaryXsSmall(
        title: 'Cài đặt giá SL/TP',
        onPressed: onSetStopLoss,
      ),
    );
  }

  /// Builds the pending TPSL order information for when isTpsl = "N"
  Widget _buildPendingTpslInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with title and arrow
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Điều kiện SL/TP (Danh mục)',
              style: context.textStyle.body14?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 12,
                color: vpColor.textPrimary,
              ),
            ),
            Icon(
              Icons.keyboard_arrow_right,
              size: 16,
              color: vpColor.textPrimary,
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Market price information
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Thị giá',
              style: context.textStyle.body14?.copyWith(
                color: vpColor.textSecondary,
              ),
            ),
            Text(
              '1,050.5',
              style: context.textStyle.body14?.copyWith(
                color: vpColor.textPriceGreen,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),

        // SL/TP conditions container
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildConditionItem(
              context,
              'Stop Loss',
              '5.5%',
              vpColor.textPriceRed,
            ),
            Container(height: 48, color: vpColor.strokeNormal, width: 1),
            _buildConditionItem(
              context,
              'Take Profit',
              '6.2%',
              vpColor.textPriceGreen,
            ),
            Container(height: 48, color: vpColor.strokeNormal, width: 1),
            _buildConditionItem(
              context,
              'Trạng thái',
              'Chờ kích hoạt',
              vpColor.textAccentBlue,
              isStatus: true,
            ),
          ],
        ),
      ],
    );
  }

  /// Builds individual condition item (SL/TP/Status)
  Widget _buildConditionItem(
    BuildContext context,
    String label,
    String value,
    Color valueColor, {
    bool isStatus = false,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: context.textStyle.body14?.copyWith(
            color: vpColor.textSecondary,
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: isStatus ? 0 : 16,
            vertical: 4,
          ),
          decoration: BoxDecoration(
            color: valueColor.withValues(alpha: 0.16),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            value,
            style: context.textStyle.body14?.copyWith(
              color: valueColor,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  /// Gets the appropriate color for P&L values
  Color _getPLColor(num? value) {
    if (value == null || value == 0) {
      return vpColor.textPrimary;
    }
    return value > 0 ? vpColor.textPriceGreen : vpColor.textPriceRed;
  }
}
