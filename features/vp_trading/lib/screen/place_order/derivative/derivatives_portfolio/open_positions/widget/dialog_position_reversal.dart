import 'package:flutter/material.dart';
import 'package:vp_common/generated/l10n.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/model/positions/open_position_model.dart';

void dialogPositionReversal(
  BuildContext context,
  OpenPositionModel position,
  VoidCallback onConfirmCallback,
) async {
  VPPopup.custom(
        padding: const EdgeInsets.all(20),
        child: const _ContentPositionReversal(),
      )
      .copyWith(
        button: VpsButton.secondaryXsSmall(
          title: 'Huỷ',
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      )
      .copyWith(
        button: VpsButton.primaryDangerXsSmall(
          title: VPCommonLocalize.current.confirm,
          onPressed: () {
            Navigator.of(context).pop();
            onConfirmCallback.call();
          },
        ),
      )
      .showDialog(context);
}

class _ContentPositionReversal extends StatelessWidget {
  const _ContentPositionReversal({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        "23423432",
        style: vpTextStyle.headineBold6?.copyWith(color: vpColor.textPrimary),
        textAlign: TextAlign.center,
      ),
    );
  }
}
