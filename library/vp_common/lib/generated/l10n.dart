// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class VPCommonLocalize {
  VPCommonLocalize();

  static VPCommonLocalize? _current;

  static VPCommonLocalize get current {
    assert(
      _current != null,
      'No instance of VPCommonLocalize was loaded. Try to initialize the VPCommonLocalize delegate before accessing VPCommonLocalize.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<VPCommonLocalize> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = VPCommonLocalize();
      VPCommonLocalize._current = instance;

      return instance;
    });
  }

  static VPCommonLocalize of(BuildContext context) {
    final instance = VPCommonLocalize.maybeOf(context);
    assert(
      instance != null,
      'No instance of VPCommonLocalize present in the widget tree. Did you add VPCommonLocalize.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static VPCommonLocalize? maybeOf(BuildContext context) {
    return Localizations.of<VPCommonLocalize>(context, VPCommonLocalize);
  }

  /// `Account`
  String get account {
    return Intl.message('Account', name: 'account', desc: '', args: []);
  }

  /// `Loading, please wait`
  String get loading {
    return Intl.message(
      'Loading, please wait',
      name: 'loading',
      desc: '',
      args: [],
    );
  }

  /// `All sub-account`
  String get all_sub_account {
    return Intl.message(
      'All sub-account',
      name: 'all_sub_account',
      desc: '',
      args: [],
    );
  }

  /// `Sub-account`
  String get sub_account {
    return Intl.message('Sub-account', name: 'sub_account', desc: '', args: []);
  }

  /// `k`
  String get k {
    return Intl.message('k', name: 'k', desc: '', args: []);
  }

  /// `mil`
  String get mil {
    return Intl.message('mil', name: 'mil', desc: '', args: []);
  }

  /// `billion`
  String get bil {
    return Intl.message('billion', name: 'bil', desc: '', args: []);
  }

  /// `TT`
  String get tt {
    return Intl.message('TT', name: 'tt', desc: '', args: []);
  }

  /// `TV`
  String get tv {
    return Intl.message('TV', name: 'tv', desc: '', args: []);
  }

  /// `TS`
  String get ts {
    return Intl.message('TS', name: 'ts', desc: '', args: []);
  }

  /// `đ`
  String get vnd {
    return Intl.message('đ', name: 'vnd', desc: '', args: []);
  }

  /// `Your connection to the system failed. Please try again.`
  String get err_default {
    return Intl.message(
      'Your connection to the system failed. Please try again.',
      name: 'err_default',
      desc: '',
      args: [],
    );
  }

  /// `Please double check your internet connection`
  String get err_internet {
    return Intl.message(
      'Please double check your internet connection',
      name: 'err_internet',
      desc: '',
      args: [],
    );
  }

  /// `An error occurred. Please try again`
  String get err_unknown {
    return Intl.message(
      'An error occurred. Please try again',
      name: 'err_unknown',
      desc: '',
      args: [],
    );
  }

  /// `Your connection is unstable. Please try again or contact support.`
  String get err_timeout {
    return Intl.message(
      'Your connection is unstable. Please try again or contact support.',
      name: 'err_timeout',
      desc: '',
      args: [],
    );
  }

  /// `No data`
  String get no_data {
    return Intl.message('No data', name: 'no_data', desc: '', args: []);
  }

  /// `Failed to retrieve data, please try again`
  String get error {
    return Intl.message(
      'Failed to retrieve data, please try again',
      name: 'error',
      desc: '',
      args: [],
    );
  }

  /// `Completed`
  String get completed {
    return Intl.message('Completed', name: 'completed', desc: '', args: []);
  }

  /// `View Account Info, Setup Settings and Help`
  String get tutorial_home1 {
    return Intl.message(
      'View Account Info, Setup Settings and Help',
      name: 'tutorial_home1',
      desc: '',
      args: [],
    );
  }

  /// `Place Stock Orders, Hold Portfolio, Order Book, Market Info, Watchlist and other stock widgets`
  String get tutorial_home2 {
    return Intl.message(
      'Place Stock Orders, Hold Portfolio, Order Book, Market Info, Watchlist and other stock widgets',
      name: 'tutorial_home2',
      desc: '',
      args: [],
    );
  }

  /// `Place derivative orders`
  String get tutorial_home3 {
    return Intl.message(
      'Place derivative orders',
      name: 'tutorial_home3',
      desc: '',
      args: [],
    );
  }

  /// `Using Bonds, eInvest`
  String get tutorial_home4 {
    return Intl.message(
      'Using Bonds, eInvest',
      name: 'tutorial_home4',
      desc: '',
      args: [],
    );
  }

  /// `View assets you are holding, Statistics of Money and operations Deposit, Transfer money`
  String get tutorial_home5 {
    return Intl.message(
      'View assets you are holding, Statistics of Money and operations Deposit, Transfer money',
      name: 'tutorial_home5',
      desc: '',
      args: [],
    );
  }

  /// `Select your watch category, edit the category name, order the codes and remove the codes from the category`
  String get tutorial_stock1 {
    return Intl.message(
      'Select your watch category, edit the category name, order the codes and remove the codes from the category',
      name: 'tutorial_stock1',
      desc: '',
      args: [],
    );
  }

  /// `Add stock ticker to your self-created portfolio`
  String get tutorial_stock2 {
    return Intl.message(
      'Add stock ticker to your self-created portfolio',
      name: 'tutorial_stock2',
      desc: '',
      args: [],
    );
  }

  /// `Stock filter, Market information, Stock order confirmation, Profit and loss log, Cash advance and other utilities`
  String get tutorial_stock3 {
    return Intl.message(
      'Stock filter, Market information, Stock order confirmation, Profit and loss log, Cash advance and other utilities',
      name: 'tutorial_stock3',
      desc: '',
      args: [],
    );
  }

  /// `View details of Money items in the account`
  String get tutorial_assets1 {
    return Intl.message(
      'View details of Money items in the account',
      name: 'tutorial_assets1',
      desc: '',
      args: [],
    );
  }

  /// `See detailed value of the product portfolio you are holding`
  String get tutorial_assets2 {
    return Intl.message(
      'See detailed value of the product portfolio you are holding',
      name: 'tutorial_assets2',
      desc: '',
      args: [],
    );
  }

  /// `View details of advances, deposits and fees`
  String get tutorial_assets3 {
    return Intl.message(
      'View details of advances, deposits and fees',
      name: 'tutorial_assets3',
      desc: '',
      args: [],
    );
  }

  /// `Information about the Bond ticker, Place a Buy Order`
  String get tutorial_bond1 {
    return Intl.message(
      'Information about the Bond ticker, Place a Buy Order',
      name: 'tutorial_bond1',
      desc: '',
      args: [],
    );
  }

  /// `View referral orders to buy Bonds from Carers`
  String get tutorial_bond2 {
    return Intl.message(
      'View referral orders to buy Bonds from Carers',
      name: 'tutorial_bond2',
      desc: '',
      args: [],
    );
  }

  /// `To place a stock order, please enter order information and stock code`
  String get tutorial_order1 {
    return Intl.message(
      'To place a stock order, please enter order information and stock code',
      name: 'tutorial_order1',
      desc: '',
      args: [],
    );
  }

  /// `Market information of the selected token`
  String get tutorial_order2 {
    return Intl.message(
      'Market information of the selected token',
      name: 'tutorial_order2',
      desc: '',
      args: [],
    );
  }

  /// `Enter the price and volume of the selected ticker to complete the stock order`
  String get tutorial_order3 {
    return Intl.message(
      'Enter the price and volume of the selected ticker to complete the stock order',
      name: 'tutorial_order3',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get next {
    return Intl.message('Next', name: 'next', desc: '', args: []);
  }

  /// `Skip`
  String get skip {
    return Intl.message('Skip', name: 'skip', desc: '', args: []);
  }

  /// `Cancel`
  String get cancel {
    return Intl.message('Cancel', name: 'cancel', desc: '', args: []);
  }

  /// `Close`
  String get close {
    return Intl.message('Close', name: 'close', desc: '', args: []);
  }

  /// `Derivative sub-account`
  String get subAccount_derivative {
    return Intl.message(
      'Derivative sub-account',
      name: 'subAccount_derivative',
      desc: '',
      args: [],
    );
  }

  /// `Standard sub-account`
  String get subAccount_standard {
    return Intl.message(
      'Standard sub-account',
      name: 'subAccount_standard',
      desc: '',
      args: [],
    );
  }

  /// `Margin sub-account`
  String get subAccount_margin {
    return Intl.message(
      'Margin sub-account',
      name: 'subAccount_margin',
      desc: '',
      args: [],
    );
  }

  /// `Bond sub-account`
  String get subAccount_bond {
    return Intl.message(
      'Bond sub-account',
      name: 'subAccount_bond',
      desc: '',
      args: [],
    );
  }

  /// `Entrusted sub-account`
  String get subAccount_entrusted {
    return Intl.message(
      'Entrusted sub-account',
      name: 'subAccount_entrusted',
      desc: '',
      args: [],
    );
  }

  /// `Wealth sub-account`
  String get subAccount_wealth {
    return Intl.message(
      'Wealth sub-account',
      name: 'subAccount_wealth',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get all {
    return Intl.message('All', name: 'all', desc: '', args: []);
  }

  /// `Save`
  String get save {
    return Intl.message('Save', name: 'save', desc: '', args: []);
  }

  /// `Accept`
  String get accept {
    return Intl.message('Accept', name: 'accept', desc: '', args: []);
  }

  /// `Ngày`
  String get date {
    return Intl.message('Ngày', name: 'date', desc: '', args: []);
  }

  /// `tháng`
  String get month {
    return Intl.message('tháng', name: 'month', desc: '', args: []);
  }

  /// `year`
  String get year {
    return Intl.message('year', name: 'year', desc: '', args: []);
  }

  /// `Áp dụng`
  String get apply {
    return Intl.message('Áp dụng', name: 'apply', desc: '', args: []);
  }

  /// `No network connection, please try again`
  String get no_internet {
    return Intl.message(
      'No network connection, please try again',
      name: 'no_internet',
      desc: '',
      args: [],
    );
  }

  /// `There are no results matching the filter. Please change the filter condition and try again.`
  String get no_filter {
    return Intl.message(
      'There are no results matching the filter. Please change the filter condition and try again.',
      name: 'no_filter',
      desc: '',
      args: [],
    );
  }

  /// `Option`
  String get option {
    return Intl.message('Option', name: 'option', desc: '', args: []);
  }

  /// `Retry`
  String get retry {
    return Intl.message('Retry', name: 'retry', desc: '', args: []);
  }

  /// `Reset`
  String get reset {
    return Intl.message('Reset', name: 'reset', desc: '', args: []);
  }

  /// `Search`
  String get search {
    return Intl.message('Search', name: 'search', desc: '', args: []);
  }

  /// `There are no matching results`
  String get noDataMatching {
    return Intl.message(
      'There are no matching results',
      name: 'noDataMatching',
      desc: '',
      args: [],
    );
  }

  /// `Not get data`
  String get notGetData {
    return Intl.message('Not get data', name: 'notGetData', desc: '', args: []);
  }

  /// `Confirm`
  String get confirm {
    return Intl.message('Confirm', name: 'confirm', desc: '', args: []);
  }

  /// `Something error. Please try again`
  String get somethingError {
    return Intl.message(
      'Something error. Please try again',
      name: 'somethingError',
      desc: '',
      args: [],
    );
  }

  /// `Understood`
  String get understood {
    return Intl.message('Understood', name: 'understood', desc: '', args: []);
  }

  /// `Download Fail`
  String get download_fail {
    return Intl.message(
      'Download Fail',
      name: 'download_fail',
      desc: '',
      args: [],
    );
  }

  /// `Download Success`
  String get download_success {
    return Intl.message(
      'Download Success',
      name: 'download_success',
      desc: '',
      args: [],
    );
  }

  /// `Downloading`
  String get downloading {
    return Intl.message('Downloading', name: 'downloading', desc: '', args: []);
  }

  /// `Back`
  String get back {
    return Intl.message('Back', name: 'back', desc: '', args: []);
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<VPCommonLocalize> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'vi'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<VPCommonLocalize> load(Locale locale) => VPCommonLocalize.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
