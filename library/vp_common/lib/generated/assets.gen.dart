/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/arrow_drop.svg
  SvgGenImage get arrowDrop => const SvgGenImage('assets/icons/arrow_drop.svg');

  /// File path: assets/icons/arrow_up.svg
  SvgGenImage get arrowUp => const SvgGenImage('assets/icons/arrow_up.svg');

  /// File path: assets/icons/calendar.svg
  SvgGenImage get calendar => const SvgGenImage('assets/icons/calendar.svg');

  /// Directory path: assets/icons/edit
  $AssetsIconsEditGen get edit => const $AssetsIconsEditGen();

  /// File path: assets/icons/ic_arrow_back.svg
  SvgGenImage get icArrowBack =>
      const SvgGenImage('assets/icons/ic_arrow_back.svg');

  /// File path: assets/icons/ic_arrow_decrease.svg
  SvgGenImage get icArrowDecrease =>
      const SvgGenImage('assets/icons/ic_arrow_decrease.svg');

  /// File path: assets/icons/ic_arrow_down.svg
  SvgGenImage get icArrowDown =>
      const SvgGenImage('assets/icons/ic_arrow_down.svg');

  /// File path: assets/icons/ic_arrow_increase.svg
  SvgGenImage get icArrowIncrease =>
      const SvgGenImage('assets/icons/ic_arrow_increase.svg');

  /// File path: assets/icons/ic_arrow_left.svg
  SvgGenImage get icArrowLeft =>
      const SvgGenImage('assets/icons/ic_arrow_left.svg');

  /// File path: assets/icons/ic_arrow_left_2.svg
  SvgGenImage get icArrowLeft2 =>
      const SvgGenImage('assets/icons/ic_arrow_left_2.svg');

  /// File path: assets/icons/ic_arrow_next.svg
  SvgGenImage get icArrowNext =>
      const SvgGenImage('assets/icons/ic_arrow_next.svg');

  /// File path: assets/icons/ic_arrow_right.svg
  SvgGenImage get icArrowRight =>
      const SvgGenImage('assets/icons/ic_arrow_right.svg');

  /// File path: assets/icons/ic_arrow_right_2.svg
  SvgGenImage get icArrowRight2 =>
      const SvgGenImage('assets/icons/ic_arrow_right_2.svg');

  /// File path: assets/icons/ic_cancel.svg
  SvgGenImage get icCancel => const SvgGenImage('assets/icons/ic_cancel.svg');

  /// File path: assets/icons/ic_check2.svg
  SvgGenImage get icCheck2 => const SvgGenImage('assets/icons/ic_check2.svg');

  /// File path: assets/icons/ic_checkbox.svg
  SvgGenImage get icCheckbox =>
      const SvgGenImage('assets/icons/ic_checkbox.svg');

  /// File path: assets/icons/ic_checkbox_none.svg
  SvgGenImage get icCheckboxNone =>
      const SvgGenImage('assets/icons/ic_checkbox_none.svg');

  /// File path: assets/icons/ic_checkbox_none_rect.svg
  SvgGenImage get icCheckboxNoneRect =>
      const SvgGenImage('assets/icons/ic_checkbox_none_rect.svg');

  /// File path: assets/icons/ic_checkbox_rect.svg
  SvgGenImage get icCheckboxRect =>
      const SvgGenImage('assets/icons/ic_checkbox_rect.svg');

  /// File path: assets/icons/ic_close.svg
  SvgGenImage get icClose => const SvgGenImage('assets/icons/ic_close.svg');

  /// File path: assets/icons/ic_cross_circle.svg
  SvgGenImage get icCrossCircle =>
      const SvgGenImage('assets/icons/ic_cross_circle.svg');

  /// File path: assets/icons/ic_derivative_no_account.svg
  SvgGenImage get icDerivativeNoAccount =>
      const SvgGenImage('assets/icons/ic_derivative_no_account.svg');

  /// File path: assets/icons/ic_dot.svg
  SvgGenImage get icDot => const SvgGenImage('assets/icons/ic_dot.svg');

  /// File path: assets/icons/ic_dot_green.svg
  SvgGenImage get icDotGreen =>
      const SvgGenImage('assets/icons/ic_dot_green.svg');

  /// File path: assets/icons/ic_dot_grey.svg
  SvgGenImage get icDotGrey =>
      const SvgGenImage('assets/icons/ic_dot_grey.svg');

  /// File path: assets/icons/ic_dot_red.svg
  SvgGenImage get icDotRed => const SvgGenImage('assets/icons/ic_dot_red.svg');

  /// File path: assets/icons/ic_down.svg
  SvgGenImage get icDown => const SvgGenImage('assets/icons/ic_down.svg');

  /// File path: assets/icons/ic_edit_primary.svg
  SvgGenImage get icEditPrimary =>
      const SvgGenImage('assets/icons/ic_edit_primary.svg');

  /// File path: assets/icons/ic_fail.svg
  SvgGenImage get icFail => const SvgGenImage('assets/icons/ic_fail.svg');

  /// File path: assets/icons/ic_fail_new.svg
  SvgGenImage get icFailNew =>
      const SvgGenImage('assets/icons/ic_fail_new.svg');

  /// File path: assets/icons/ic_failed.svg
  SvgGenImage get icFailed => const SvgGenImage('assets/icons/ic_failed.svg');

  /// File path: assets/icons/ic_off.svg
  SvgGenImage get icOff => const SvgGenImage('assets/icons/ic_off.svg');

  /// File path: assets/icons/ic_on.svg
  SvgGenImage get icOn => const SvgGenImage('assets/icons/ic_on.svg');

  /// File path: assets/icons/ic_right.svg
  SvgGenImage get icRight => const SvgGenImage('assets/icons/ic_right.svg');

  /// File path: assets/icons/ic_sort_decrease.svg
  SvgGenImage get icSortDecrease =>
      const SvgGenImage('assets/icons/ic_sort_decrease.svg');

  /// File path: assets/icons/ic_sort_increase.svg
  SvgGenImage get icSortIncrease =>
      const SvgGenImage('assets/icons/ic_sort_increase.svg');

  /// File path: assets/icons/ic_success.svg
  SvgGenImage get icSuccess => const SvgGenImage('assets/icons/ic_success.svg');

  /// File path: assets/icons/ic_success_new.svg
  SvgGenImage get icSuccessNew =>
      const SvgGenImage('assets/icons/ic_success_new.svg');

  /// File path: assets/icons/ic_un_check.svg
  SvgGenImage get icUnCheck =>
      const SvgGenImage('assets/icons/ic_un_check.svg');

  /// File path: assets/icons/ic_up.svg
  SvgGenImage get icUp => const SvgGenImage('assets/icons/ic_up.svg');

  /// File path: assets/icons/ic_up_2.svg
  SvgGenImage get icUp2 => const SvgGenImage('assets/icons/ic_up_2.svg');

  /// File path: assets/icons/ic_warning.svg
  SvgGenImage get icWarning => const SvgGenImage('assets/icons/ic_warning.svg');

  /// File path: assets/icons/ic_warning_2.svg
  SvgGenImage get icWarning2 =>
      const SvgGenImage('assets/icons/ic_warning_2.svg');

  /// File path: assets/icons/ic_warning_3.svg
  SvgGenImage get icWarning3 =>
      const SvgGenImage('assets/icons/ic_warning_3.svg');

  /// Directory path: assets/icons/info
  $AssetsIconsInfoGen get info => const $AssetsIconsInfoGen();

  /// File path: assets/icons/none.png
  AssetGenImage get none => const AssetGenImage('assets/icons/none.png');

  /// File path: assets/icons/radio.svg
  SvgGenImage get radio => const SvgGenImage('assets/icons/radio.svg');

  /// File path: assets/icons/radio2.svg
  SvgGenImage get radio2 => const SvgGenImage('assets/icons/radio2.svg');

  /// File path: assets/icons/search.svg
  SvgGenImage get search => const SvgGenImage('assets/icons/search.svg');

  /// List of all assets
  List<dynamic> get values => [
    arrowDrop,
    arrowUp,
    calendar,
    icArrowBack,
    icArrowDecrease,
    icArrowDown,
    icArrowIncrease,
    icArrowLeft,
    icArrowLeft2,
    icArrowNext,
    icArrowRight,
    icArrowRight2,
    icCancel,
    icCheck2,
    icCheckbox,
    icCheckboxNone,
    icCheckboxNoneRect,
    icCheckboxRect,
    icClose,
    icCrossCircle,
    icDerivativeNoAccount,
    icDot,
    icDotGreen,
    icDotGrey,
    icDotRed,
    icDown,
    icEditPrimary,
    icFail,
    icFailNew,
    icFailed,
    icOff,
    icOn,
    icRight,
    icSortDecrease,
    icSortIncrease,
    icSuccess,
    icSuccessNew,
    icUnCheck,
    icUp,
    icUp2,
    icWarning,
    icWarning2,
    icWarning3,
    none,
    radio,
    radio2,
    search,
  ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/error.png
  AssetGenImage get error => const AssetGenImage('assets/images/error.png');

  /// File path: assets/images/find.png
  AssetGenImage get find => const AssetGenImage('assets/images/find.png');

  /// File path: assets/images/logo.png
  AssetGenImage get logo => const AssetGenImage('assets/images/logo.png');

  /// File path: assets/images/search.png
  AssetGenImage get search => const AssetGenImage('assets/images/search.png');

  /// File path: assets/images/small_logo.png
  AssetGenImage get smallLogo =>
      const AssetGenImage('assets/images/small_logo.png');

  /// Directory path: assets/images/tutorial
  $AssetsImagesTutorialGen get tutorial => const $AssetsImagesTutorialGen();

  /// List of all assets
  List<AssetGenImage> get values => [error, find, logo, search, smallLogo];
}

class $AssetsJsonGen {
  const $AssetsJsonGen();

  /// File path: assets/json/loading_logo.json
  String get loadingLogo => 'packages/vp_common/assets/json/loading_logo.json';

  /// List of all assets
  List<String> get values => [loadingLogo];
}

class $AssetsIconsEditGen {
  const $AssetsIconsEditGen();

  /// File path: assets/icons/edit/ic_edit.svg
  SvgGenImage get icEdit => const SvgGenImage('assets/icons/edit/ic_edit.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icEdit];
}

class $AssetsIconsInfoGen {
  const $AssetsIconsInfoGen();

  /// File path: assets/icons/info/ic_info.svg
  SvgGenImage get icInfo => const SvgGenImage('assets/icons/info/ic_info.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icInfo];
}

class $AssetsImagesTutorialGen {
  const $AssetsImagesTutorialGen();

  /// Directory path: assets/images/tutorial/dark
  $AssetsImagesTutorialDarkGen get dark => const $AssetsImagesTutorialDarkGen();

  /// Directory path: assets/images/tutorial/light
  $AssetsImagesTutorialLightGen get light =>
      const $AssetsImagesTutorialLightGen();
}

class $AssetsImagesTutorialDarkGen {
  const $AssetsImagesTutorialDarkGen();

  /// File path: assets/images/tutorial/dark/assets1.png
  AssetGenImage get assets1 =>
      const AssetGenImage('assets/images/tutorial/dark/assets1.png');

  /// File path: assets/images/tutorial/dark/assets2.png
  AssetGenImage get assets2 =>
      const AssetGenImage('assets/images/tutorial/dark/assets2.png');

  /// File path: assets/images/tutorial/dark/assets3.png
  AssetGenImage get assets3 =>
      const AssetGenImage('assets/images/tutorial/dark/assets3.png');

  /// File path: assets/images/tutorial/dark/bond1.png
  AssetGenImage get bond1 =>
      const AssetGenImage('assets/images/tutorial/dark/bond1.png');

  /// File path: assets/images/tutorial/dark/bond2.png
  AssetGenImage get bond2 =>
      const AssetGenImage('assets/images/tutorial/dark/bond2.png');

  /// File path: assets/images/tutorial/dark/home1.png
  AssetGenImage get home1 =>
      const AssetGenImage('assets/images/tutorial/dark/home1.png');

  /// File path: assets/images/tutorial/dark/home2.png
  AssetGenImage get home2 =>
      const AssetGenImage('assets/images/tutorial/dark/home2.png');

  /// File path: assets/images/tutorial/dark/home3.png
  AssetGenImage get home3 =>
      const AssetGenImage('assets/images/tutorial/dark/home3.png');

  /// File path: assets/images/tutorial/dark/home4.png
  AssetGenImage get home4 =>
      const AssetGenImage('assets/images/tutorial/dark/home4.png');

  /// File path: assets/images/tutorial/dark/home5.png
  AssetGenImage get home5 =>
      const AssetGenImage('assets/images/tutorial/dark/home5.png');

  /// File path: assets/images/tutorial/dark/order1.png
  AssetGenImage get order1 =>
      const AssetGenImage('assets/images/tutorial/dark/order1.png');

  /// File path: assets/images/tutorial/dark/order2.png
  AssetGenImage get order2 =>
      const AssetGenImage('assets/images/tutorial/dark/order2.png');

  /// File path: assets/images/tutorial/dark/order3.png
  AssetGenImage get order3 =>
      const AssetGenImage('assets/images/tutorial/dark/order3.png');

  /// File path: assets/images/tutorial/dark/order_new_intro_1.png
  AssetGenImage get orderNewIntro1 =>
      const AssetGenImage('assets/images/tutorial/dark/order_new_intro_1.png');

  /// File path: assets/images/tutorial/dark/order_new_intro_2.png
  AssetGenImage get orderNewIntro2 =>
      const AssetGenImage('assets/images/tutorial/dark/order_new_intro_2.png');

  /// File path: assets/images/tutorial/dark/stock1.png
  AssetGenImage get stock1 =>
      const AssetGenImage('assets/images/tutorial/dark/stock1.png');

  /// File path: assets/images/tutorial/dark/stock2.png
  AssetGenImage get stock2 =>
      const AssetGenImage('assets/images/tutorial/dark/stock2.png');

  /// File path: assets/images/tutorial/dark/stock3.png
  AssetGenImage get stock3 =>
      const AssetGenImage('assets/images/tutorial/dark/stock3.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    assets1,
    assets2,
    assets3,
    bond1,
    bond2,
    home1,
    home2,
    home3,
    home4,
    home5,
    order1,
    order2,
    order3,
    orderNewIntro1,
    orderNewIntro2,
    stock1,
    stock2,
    stock3,
  ];
}

class $AssetsImagesTutorialLightGen {
  const $AssetsImagesTutorialLightGen();

  /// File path: assets/images/tutorial/light/assets1.png
  AssetGenImage get assets1 =>
      const AssetGenImage('assets/images/tutorial/light/assets1.png');

  /// File path: assets/images/tutorial/light/assets2.png
  AssetGenImage get assets2 =>
      const AssetGenImage('assets/images/tutorial/light/assets2.png');

  /// File path: assets/images/tutorial/light/assets3.png
  AssetGenImage get assets3 =>
      const AssetGenImage('assets/images/tutorial/light/assets3.png');

  /// File path: assets/images/tutorial/light/bond1.png
  AssetGenImage get bond1 =>
      const AssetGenImage('assets/images/tutorial/light/bond1.png');

  /// File path: assets/images/tutorial/light/bond2.png
  AssetGenImage get bond2 =>
      const AssetGenImage('assets/images/tutorial/light/bond2.png');

  /// File path: assets/images/tutorial/light/home1.png
  AssetGenImage get home1 =>
      const AssetGenImage('assets/images/tutorial/light/home1.png');

  /// File path: assets/images/tutorial/light/home2.png
  AssetGenImage get home2 =>
      const AssetGenImage('assets/images/tutorial/light/home2.png');

  /// File path: assets/images/tutorial/light/home3.png
  AssetGenImage get home3 =>
      const AssetGenImage('assets/images/tutorial/light/home3.png');

  /// File path: assets/images/tutorial/light/home4.png
  AssetGenImage get home4 =>
      const AssetGenImage('assets/images/tutorial/light/home4.png');

  /// File path: assets/images/tutorial/light/home5.png
  AssetGenImage get home5 =>
      const AssetGenImage('assets/images/tutorial/light/home5.png');

  /// File path: assets/images/tutorial/light/order1.png
  AssetGenImage get order1 =>
      const AssetGenImage('assets/images/tutorial/light/order1.png');

  /// File path: assets/images/tutorial/light/order2.png
  AssetGenImage get order2 =>
      const AssetGenImage('assets/images/tutorial/light/order2.png');

  /// File path: assets/images/tutorial/light/order3.png
  AssetGenImage get order3 =>
      const AssetGenImage('assets/images/tutorial/light/order3.png');

  /// File path: assets/images/tutorial/light/order_new_intro_1.png
  AssetGenImage get orderNewIntro1 =>
      const AssetGenImage('assets/images/tutorial/light/order_new_intro_1.png');

  /// File path: assets/images/tutorial/light/order_new_intro_2.png
  AssetGenImage get orderNewIntro2 =>
      const AssetGenImage('assets/images/tutorial/light/order_new_intro_2.png');

  /// File path: assets/images/tutorial/light/stock1.png
  AssetGenImage get stock1 =>
      const AssetGenImage('assets/images/tutorial/light/stock1.png');

  /// File path: assets/images/tutorial/light/stock2.png
  AssetGenImage get stock2 =>
      const AssetGenImage('assets/images/tutorial/light/stock2.png');

  /// File path: assets/images/tutorial/light/stock3.png
  AssetGenImage get stock3 =>
      const AssetGenImage('assets/images/tutorial/light/stock3.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    assets1,
    assets2,
    assets3,
    bond1,
    bond2,
    home1,
    home2,
    home3,
    home4,
    home5,
    order1,
    order2,
    order3,
    orderNewIntro1,
    orderNewIntro2,
    stock1,
    stock2,
    stock3,
  ];
}

class Assets {
  const Assets._();

  static const String package = 'vp_common';

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsJsonGen json = $AssetsJsonGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  static const String package = 'vp_common';

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
  }) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_common/$_assetName';
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  static const String package = 'vp_common';

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_common/$_assetName';
}
