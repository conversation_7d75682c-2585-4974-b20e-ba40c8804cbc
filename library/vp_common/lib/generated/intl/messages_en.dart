// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "accept": MessageLookupByLibrary.simpleMessage("Accept"),
    "account": MessageLookupByLibrary.simpleMessage("Account"),
    "all": MessageLookupByLibrary.simpleMessage("All"),
    "all_sub_account": MessageLookupByLibrary.simpleMessage("All sub-account"),
    "apply": MessageLookupByLibrary.simpleMessage("Áp dụng"),
    "back": MessageLookupByLibrary.simpleMessage("Back"),
    "bil": MessageLookupByLibrary.simpleMessage("billion"),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "close": MessageLookupByLibrary.simpleMessage("Close"),
    "completed": MessageLookupByLibrary.simpleMessage("Completed"),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "date": MessageLookupByLibrary.simpleMessage("Ngày"),
    "download_fail": MessageLookupByLibrary.simpleMessage("Download Fail"),
    "download_success": MessageLookupByLibrary.simpleMessage(
      "Download Success",
    ),
    "downloading": MessageLookupByLibrary.simpleMessage("Downloading"),
    "err_default": MessageLookupByLibrary.simpleMessage(
      "Your connection to the system failed. Please try again.",
    ),
    "err_internet": MessageLookupByLibrary.simpleMessage(
      "Please double check your internet connection",
    ),
    "err_timeout": MessageLookupByLibrary.simpleMessage(
      "Your connection is unstable. Please try again or contact support.",
    ),
    "err_unknown": MessageLookupByLibrary.simpleMessage(
      "An error occurred. Please try again",
    ),
    "error": MessageLookupByLibrary.simpleMessage(
      "Failed to retrieve data, please try again",
    ),
    "k": MessageLookupByLibrary.simpleMessage("k"),
    "loading": MessageLookupByLibrary.simpleMessage("Loading, please wait"),
    "mil": MessageLookupByLibrary.simpleMessage("mil"),
    "month": MessageLookupByLibrary.simpleMessage("tháng"),
    "next": MessageLookupByLibrary.simpleMessage("Next"),
    "noDataMatching": MessageLookupByLibrary.simpleMessage(
      "There are no matching results",
    ),
    "no_data": MessageLookupByLibrary.simpleMessage("No data"),
    "no_filter": MessageLookupByLibrary.simpleMessage(
      "There are no results matching the filter. Please change the filter condition and try again.",
    ),
    "no_internet": MessageLookupByLibrary.simpleMessage(
      "No network connection, please try again",
    ),
    "notGetData": MessageLookupByLibrary.simpleMessage("Not get data"),
    "option": MessageLookupByLibrary.simpleMessage("Option"),
    "reset": MessageLookupByLibrary.simpleMessage("Reset"),
    "retry": MessageLookupByLibrary.simpleMessage("Retry"),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "somethingError": MessageLookupByLibrary.simpleMessage(
      "Something error. Please try again",
    ),
    "subAccount_bond": MessageLookupByLibrary.simpleMessage("Bond sub-account"),
    "subAccount_derivative": MessageLookupByLibrary.simpleMessage(
      "Derivative sub-account",
    ),
    "subAccount_entrusted": MessageLookupByLibrary.simpleMessage(
      "Entrusted sub-account",
    ),
    "subAccount_margin": MessageLookupByLibrary.simpleMessage(
      "Margin sub-account",
    ),
    "subAccount_standard": MessageLookupByLibrary.simpleMessage(
      "Standard sub-account",
    ),
    "subAccount_wealth": MessageLookupByLibrary.simpleMessage(
      "Wealth sub-account",
    ),
    "sub_account": MessageLookupByLibrary.simpleMessage("Sub-account"),
    "ts": MessageLookupByLibrary.simpleMessage("TS"),
    "tt": MessageLookupByLibrary.simpleMessage("TT"),
    "tutorial_assets1": MessageLookupByLibrary.simpleMessage(
      "View details of Money items in the account",
    ),
    "tutorial_assets2": MessageLookupByLibrary.simpleMessage(
      "See detailed value of the product portfolio you are holding",
    ),
    "tutorial_assets3": MessageLookupByLibrary.simpleMessage(
      "View details of advances, deposits and fees",
    ),
    "tutorial_bond1": MessageLookupByLibrary.simpleMessage(
      "Information about the Bond ticker, Place a Buy Order",
    ),
    "tutorial_bond2": MessageLookupByLibrary.simpleMessage(
      "View referral orders to buy Bonds from Carers",
    ),
    "tutorial_home1": MessageLookupByLibrary.simpleMessage(
      "View Account Info, Setup Settings and Help",
    ),
    "tutorial_home2": MessageLookupByLibrary.simpleMessage(
      "Place Stock Orders, Hold Portfolio, Order Book, Market Info, Watchlist and other stock widgets",
    ),
    "tutorial_home3": MessageLookupByLibrary.simpleMessage(
      "Place derivative orders",
    ),
    "tutorial_home4": MessageLookupByLibrary.simpleMessage(
      "Using Bonds, eInvest",
    ),
    "tutorial_home5": MessageLookupByLibrary.simpleMessage(
      "View assets you are holding, Statistics of Money and operations Deposit, Transfer money",
    ),
    "tutorial_order1": MessageLookupByLibrary.simpleMessage(
      "To place a stock order, please enter order information and stock code",
    ),
    "tutorial_order2": MessageLookupByLibrary.simpleMessage(
      "Market information of the selected token",
    ),
    "tutorial_order3": MessageLookupByLibrary.simpleMessage(
      "Enter the price and volume of the selected ticker to complete the stock order",
    ),
    "tutorial_stock1": MessageLookupByLibrary.simpleMessage(
      "Select your watch category, edit the category name, order the codes and remove the codes from the category",
    ),
    "tutorial_stock2": MessageLookupByLibrary.simpleMessage(
      "Add stock ticker to your self-created portfolio",
    ),
    "tutorial_stock3": MessageLookupByLibrary.simpleMessage(
      "Stock filter, Market information, Stock order confirmation, Profit and loss log, Cash advance and other utilities",
    ),
    "tv": MessageLookupByLibrary.simpleMessage("TV"),
    "understood": MessageLookupByLibrary.simpleMessage("Understood"),
    "vnd": MessageLookupByLibrary.simpleMessage("đ"),
    "year": MessageLookupByLibrary.simpleMessage("year"),
  };
}
