// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "accept": MessageLookupByLibrary.simpleMessage("Đồng ý"),
    "account": MessageLookupByLibrary.simpleMessage("Tài khoản"),
    "all": MessageLookupByLibrary.simpleMessage("Tất cả"),
    "all_sub_account": MessageLookupByLibrary.simpleMessage(
      "Tất cả tiểu khoản",
    ),
    "apply": MessageLookupByLibrary.simpleMessage("Áp dụng"),
    "back": MessageLookupByLibrary.simpleMessage("Quay lại"),
    "bil": MessageLookupByLibrary.simpleMessage("tỷ"),
    "cancel": MessageLookupByLibrary.simpleMessage("Huỷ"),
    "close": MessageLookupByLibrary.simpleMessage("Đóng"),
    "completed": MessageLookupByLibrary.simpleMessage("Hoàn tất"),
    "confirm": MessageLookupByLibrary.simpleMessage("Xác nhận"),
    "date": MessageLookupByLibrary.simpleMessage("Ngày"),
    "download_fail": MessageLookupByLibrary.simpleMessage("Tải xuống thất bại"),
    "download_success": MessageLookupByLibrary.simpleMessage(
      "Tải xuống thành công",
    ),
    "downloading": MessageLookupByLibrary.simpleMessage("Đang tải xuống"),
    "err_default": MessageLookupByLibrary.simpleMessage(
      "Kết nối mạng không ổn định, vui lòng thử lại.",
    ),
    "err_internet": MessageLookupByLibrary.simpleMessage(
      "Vui lòng kiểm tra lại kết nối internet của bạn.",
    ),
    "err_timeout": MessageLookupByLibrary.simpleMessage(
      "Kết nối của bạn không ổn định. Vui lòng thử lại hoặc liên hệ bộ phận hỗ trợ.",
    ),
    "err_unknown": MessageLookupByLibrary.simpleMessage(
      "Có lỗi xảy ra. Vui lòng thử lại.",
    ),
    "error": MessageLookupByLibrary.simpleMessage(
      "Không lấy được dữ liệu, vui lòng thử lại",
    ),
    "k": MessageLookupByLibrary.simpleMessage("nghìn"),
    "loading": MessageLookupByLibrary.simpleMessage("Đang tải, vui lòng chờ"),
    "mil": MessageLookupByLibrary.simpleMessage("triệu"),
    "month": MessageLookupByLibrary.simpleMessage("tháng"),
    "next": MessageLookupByLibrary.simpleMessage("Tiếp theo"),
    "noDataMatching": MessageLookupByLibrary.simpleMessage(
      "Không có kết quả nào phù hợp",
    ),
    "no_data": MessageLookupByLibrary.simpleMessage("Không có dữ liệu"),
    "no_filter": MessageLookupByLibrary.simpleMessage(
      "Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại.",
    ),
    "no_internet": MessageLookupByLibrary.simpleMessage(
      "Không có kết nối mạng, vui lòng thử lại",
    ),
    "notGetData": MessageLookupByLibrary.simpleMessage(
      "Không lấy được dữ liệu",
    ),
    "option": MessageLookupByLibrary.simpleMessage("Tùy chọn"),
    "reset": MessageLookupByLibrary.simpleMessage("Đặt lại"),
    "retry": MessageLookupByLibrary.simpleMessage("Thử lại"),
    "save": MessageLookupByLibrary.simpleMessage("Lưu"),
    "search": MessageLookupByLibrary.simpleMessage("Tìm kiếm"),
    "skip": MessageLookupByLibrary.simpleMessage("Bỏ qua"),
    "somethingError": MessageLookupByLibrary.simpleMessage(
      "Có lỗi xảy ra khi lấy dữ liệu. Vui lòng thử lại sau",
    ),
    "subAccount_bond": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản trái phiếu",
    ),
    "subAccount_derivative": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản phái sinh",
    ),
    "subAccount_entrusted": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản ủy thác",
    ),
    "subAccount_margin": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản ký quỹ",
    ),
    "subAccount_standard": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản thường",
    ),
    "subAccount_wealth": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản wealth",
    ),
    "sub_account": MessageLookupByLibrary.simpleMessage("Tiểu khoản"),
    "ts": MessageLookupByLibrary.simpleMessage("Trạng thái GD"),
    "tt": MessageLookupByLibrary.simpleMessage("KLGD"),
    "tutorial_assets1": MessageLookupByLibrary.simpleMessage(
      "Xem chi tiết các khoản mục Tiền trong tài khoản",
    ),
    "tutorial_assets2": MessageLookupByLibrary.simpleMessage(
      "Xem chi tiết giá trị danh mục sản phẩm bạn đang nắm giữ",
    ),
    "tutorial_assets3": MessageLookupByLibrary.simpleMessage(
      "Xem chi tiết nợ ứng trước, ký quỹ và nợ phí",
    ),
    "tutorial_bond1": MessageLookupByLibrary.simpleMessage(
      "Thông tin về mã Trái phiếu, Đặt lệnh mua",
    ),
    "tutorial_bond2": MessageLookupByLibrary.simpleMessage(
      "Xem các lệnh giới thiệu mua Trái phiếu từ Nhân viên chăm sóc",
    ),
    "tutorial_home1": MessageLookupByLibrary.simpleMessage(
      "Xem thông tin tài khoản, Thiết lập cài đặt và Trợ giúp",
    ),
    "tutorial_home2": MessageLookupByLibrary.simpleMessage(
      "Đặt lệnh cổ phiếu, Danh mục nắm giữ, Sổ lệnh, Thông tin thị trường, Danh sách theo dõi và các tiện ích cổ phiếu khác",
    ),
    "tutorial_home3": MessageLookupByLibrary.simpleMessage(
      "Đặt lệnh phái sinh",
    ),
    "tutorial_home4": MessageLookupByLibrary.simpleMessage(
      "Sử dụng các sản phẩm Trái phiếu, eInvest",
    ),
    "tutorial_home5": MessageLookupByLibrary.simpleMessage(
      "Xem tài sản bạn đang nắm giữ, Thống kê Tiền và thao tác Nạp tiền, Chuyển tiền",
    ),
    "tutorial_order1": MessageLookupByLibrary.simpleMessage(
      "Để đặt lệnh cổ phiếu, bạn hãy nhập thông tin lệnh và mã chứng khoán",
    ),
    "tutorial_order2": MessageLookupByLibrary.simpleMessage(
      "Thông tin thị trường của mã đang chọn",
    ),
    "tutorial_order3": MessageLookupByLibrary.simpleMessage(
      "Nhập giá và khối lượng của mã đang chọn để hoàn tất đặt lệnh cổ phiếu",
    ),
    "tutorial_stock1": MessageLookupByLibrary.simpleMessage(
      "Chọn danh mục theo dõi của bạn, chỉnh sửa tên danh mục, thứ tự các mã và xóa mã khỏi danh mục",
    ),
    "tutorial_stock2": MessageLookupByLibrary.simpleMessage(
      "Thêm mã chứng khoán vào danh mục tự tạo của bạn",
    ),
    "tutorial_stock3": MessageLookupByLibrary.simpleMessage(
      "Bộ lọc cổ phiếu, Thông tin thị trường, Xác nhận lệnh cổ phiếu, Nhật ký lãi lỗ, Ứng trước tiền bán và các tiện ích khác",
    ),
    "tv": MessageLookupByLibrary.simpleMessage("GTGD"),
    "understood": MessageLookupByLibrary.simpleMessage("Đã hiểu"),
    "vnd": MessageLookupByLibrary.simpleMessage("đ"),
    "year": MessageLookupByLibrary.simpleMessage("năm"),
  };
}
