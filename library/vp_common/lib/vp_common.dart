/// Support for doing something awesome.
///
/// More dartdocs go here.
library vp_common;

export 'package:alice/alice.dart';
export 'package:equatable/equatable.dart';
export 'package:flutter_svg/flutter_svg.dart';
export 'package:get_it/get_it.dart';
export 'package:multiple_localization/multiple_localization.dart';
export 'package:shimmer/shimmer.dart';
export 'package:tiengviet/tiengviet.dart';

export '/utils/dynamic_to_num_converter.dart';
export 'constants/app_config_utils.dart';
export 'constants/app_constants.dart';
export 'constants/base_url.dart';
export 'constants/common_assets.dart';
export 'constants/condition_command_enum.dart';
export 'constants/const_otp.dart';
export 'constants/key_api.dart';
export 'constants/key_shared.dart';
export 'constants/status_enum.dart';
export 'download_file_manager/base_download_file_manager.dart';
export 'download_file_manager/download_file_manager.dart';
export 'error/handle_error.dart';
export 'error/show_error.dart';
export 'extensions/context_extensions.dart';
export 'extensions/date_extensions.dart';
export 'extensions/double_extensions.dart';
export 'extensions/int_extensions.dart';
export 'extensions/iterable_extensions.dart';
export 'extensions/list_extensions.dart';
export 'extensions/map_extensions.dart';
export 'extensions/num_extensions.dart';
export 'extensions/orientation_extensions.dart';
export 'extensions/phone_extensions.dart';
export 'extensions/popup_extensions.dart';
export 'extensions/price_exts.dart';
export 'extensions/string_extensions.dart';
export 'extensions/time_extension.dart';
export 'generated/assets.gen.dart';
export 'generated/l10n.dart';
export 'shared_prefs/shared_prefs.dart';
export 'utils/app_helper.dart';
export 'utils/app_keyboard_utils.dart';
export 'utils/app_log_utils.dart';
export 'utils/app_routes_utils/animate_routes.dart';
export 'utils/app_routes_utils/app_routes_utils.dart';
export 'utils/app_text_input_formatter_utils.dart';
export 'utils/app_time_utils.dart';
export 'utils/app_validate.dart';
export 'utils/download_utils.dart';
export 'utils/dynamic_to_datetime_converter.dart';
export 'utils/format_utils.dart';
export 'utils/function_utils.dart';
export 'utils/navigation_service.dart';
export 'utils/stream_utils.dart';
export 'utils/throttle.dart';
export 'widget/input_validator/input_validator_controller.dart';
export 'widget/input_validator/input_validator_label.dart';
export 'widget/input_validator_state.dart';
export 'widget/loading/loading.dart';
export 'widget/log_api/intercepter/log_api_intercepter.dart';
export 'widget/log_api/utils/log_api_module_config.dart';
export 'widget/step_bottom_view.dart';
export 'widget/vp_flashing_color_view.dart';
export 'widget/vp_image.dart';
export 'widget/vpbank_loading.dart';

