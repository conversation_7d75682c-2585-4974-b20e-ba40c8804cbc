import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:uuid/uuid.dart';

class AppHelper {
  factory AppHelper() => _singleton;

  AppHelper._internal();

  static final AppHelper _singleton = AppHelper._internal();

  String genXRequestID() {
    return const Uuid().v4();
  }

  /// Get version
  Future<String?> getVersion() async {
    final info = await PackageInfo.fromPlatform();
    return info.version;
  }

  /// Device type
  String? getDeviceType() {
    if (Platform.isAndroid) {
      return 'android';
    } else if (Platform.isIOS) {
      return 'ios';
    } else {
      return null;
    }
  }

  /// Mac Address
  Future<String?> getMacAddress() async {
    try {
      final info = NetworkInfo();
      if (Platform.isAndroid) {
        DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

        if (androidInfo.version.sdkInt >= 29) return null;
      }

      String? macAddress = await info.getWifiBSSID();
      return macAddress;
    } catch (e) {
      return null;
    }
  }
}
