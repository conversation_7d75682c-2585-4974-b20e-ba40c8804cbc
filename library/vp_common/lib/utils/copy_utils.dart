import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';

void copyToClipboard(BuildContext context, String? value,
    {String? toast}) async {
  if (value == null || value.isEmpty) return;

  try {
    await Clipboard.setData(ClipboardData(text: value));

    Fluttertoast.showToast(
      msg: toast ?? "Sao chép thành công",
      toastLength: Toast.LENGTH_SHORT,
      // gravity: ToastGravity.CENTER,
      // timeInSecForIosWeb: 1,
      // textColor: Colors.white,
      // fontSize: 16.0
    );
  } catch (e, stackTrace) {
    debugPrintStack(stackTrace: stackTrace);
  }
}
