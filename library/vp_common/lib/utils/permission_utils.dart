import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionUtils {
    static bool checkingPermission = false;

  static Future<bool> checkPermissionFile() async {
    if (!PermissionUtils.checkingPermission) {
      try {
        PermissionUtils.checkingPermission = true;

        Permission permission = Permission.storage;

        if (Platform.isAndroid) {
          final deviceInfo = await DeviceInfoPlugin().androidInfo;

          final sdkInt = deviceInfo.version.sdkInt ?? 0;

          permission = sdkInt < 33 ? Permission.storage : Permission.photos;
        } else {
          permission = Permission.storage;
        }

        final status = await permission.request();

        final isHaveDenied = status == PermissionStatus.denied ||
            status == PermissionStatus.permanentlyDenied;

        if (isHaveDenied) {
          openAppSettings();
        }

        return !isHaveDenied;
      } catch (e, stackTrace) {
        return false;
      } finally {
        PermissionUtils.checkingPermission = false;
      }
    }
    return false;
  }

  static Future<bool> requireStoragePermission(Permission permission) async {
    var newPermission = permission;

    if (Platform.isAndroid) {
      final deviceInfo = await DeviceInfoPlugin().androidInfo;

      final sdkInt = deviceInfo.version.sdkInt ?? 0;

      newPermission = sdkInt < 33 ? Permission.storage : Permission.photos;
    }

    final status = await newPermission.request();

    if (status == PermissionStatus.denied) return false;

    if (status == PermissionStatus.granted) return true;

    openAppSettings();

    return false;
  }

  static Future<bool> hasPermission(Permission permission) async {
    final status = await permission.status;

    return status == PermissionStatus.granted;
  }
}
