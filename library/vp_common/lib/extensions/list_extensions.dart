import 'package:vp_common/extensions/string_extensions.dart';

extension ListExtension<T> on List<T>? {
  T? get firstOrNull => this == null || this!.isEmpty ? null : this!.first;

  Map<K, List<T>>? groupListsBy<K>(K Function(T element) keyOf) {
    if (this == null) return null;

    final result = <K, List<T>>{};
    for (final element in this!) {
      (result[keyOf(element)] ??= []).add(element);
    }
    return result;
  }

  /// format symbol
  /// input: [AAA, TCB]
  /// output: AAA, TCB
  String get symbolsFormat {
    if (isNullOrEmpty) return '';

    final newList = [...this!]..removeWhere((e) => (e as String).isNullOrEmpty);

    return newList.join(',');
  }

  void addAllIf(List<T>? data, {bool Function()? addIf}) {
    if (addIf != null && addIf.call() == true && this != null && data != null) {
      this!.addAll(data);
    }
  }

  void addIf(T data, {bool Function()? addIf}) {
    if (addIf != null && addIf.call() == true && this != null && data != null) {
      this!.add(data);
    }
  }

  List<R>? mapIndex<R>(R Function(int index, T element) callback) {
    if (this.isNullOrEmpty) return <R>[];

    final result = <R>[];

    for (var i = 0; i < this!.length; i++) {
      result.add(callback(i, this![i]));
    }

    return result;
  }

  bool get isNullOrEmpty {
    return this == null || this!.isEmpty;
  }

  bool get hasData => !isNullOrEmpty;

  T? getElementAt(int? index) {
    if (index == null || this == null || index >= this!.length || index < 0) {
      return null;
    }

    return this?[index];
  }

  List<T> sortWith(
      {required dynamic Function(T entity) propertyGetter,
      bool ascending = true}) {
    final newList = [...?this];
    return newList
      ..sort(
        (a, b) => ascending
            ? propertyGetter(a).compareTo(propertyGetter(b))
            : propertyGetter(b).compareTo(propertyGetter(a)),
      );
  }

  List<T> sortNumWith(
      {required dynamic Function(T entity) propertyGetter,
      bool ascending = true}) {
    final newList = [...?this];
    return newList
      ..sort(
        (a, b) => ascending
            ? propertyGetter(a) - propertyGetter(b) == 0
                ? 0
                : propertyGetter(a) - propertyGetter(b) > 0
                    ? 1
                    : -1
            : propertyGetter(b) - propertyGetter(a) == 0
                ? 0
                : propertyGetter(b) - propertyGetter(a) > 0
                    ? 1
                    : -1,
      );
  }
}
