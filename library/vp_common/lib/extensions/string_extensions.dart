import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

final RegExp _urlRegExp =
    RegExp(r'(?:(?:https?|ftp):\/\/)?[\w/\-?=%.]+\.[\w/\-?=%.]+');

extension StringExts on String? {
  bool get isNullOrEmpty => this == null || this!.isEmpty;

  bool get hasData => !isNullOrEmpty;

  bool get isUrl => hasData && _urlRegExp.hasMatch(this!);

  bool get isValidDateFormat {
    final dateRegex =
        RegExp(r'^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/\d{4}$');

    if (!dateRegex.hasMatch(this!)) return false;

    final parts = this!.split('/');
    final day = int.parse(parts[0]);
    final month = int.parse(parts[1]);
    final year = int.parse(parts[2]);

    try {
      final parsedDate = DateTime(year, month, day);
      return parsedDate.day == day &&
          parsedDate.month == month &&
          parsedDate.year == year;
    } catch (e) {
      return false;
    }
  }

  String stringToDateTime() {
    try {
      return DateFormat('dd/MM/yyyy HH:mm').format(DateTime.parse(this!));
    } catch (err) {
      return '';
    }
  }

  String stringToDateDdMmYyyy() {
    try {
      return DateFormat('dd/MM/yyyy').format(DateTime.parse(this!));
    } catch (err) {
      return '';
    }
  }

  String stringToDateDdMm() {
    try {
      return DateFormat('dd/MM').format(DateTime.parse(this!));
    } catch (err) {
      return '';
    }
  }

  DateTime dateTimeFormatDDMMYyyy() {
    return DateFormat('dd/MM/yyyy').parse(this!);
  }

  DateTime? dateTimeParse(String pattern) {
    try {
      return DateFormat(pattern).parse(this ?? '');
    } catch (e) {
      return null;
    }
  }

  TimeOfDay? get hhmmssToTime {
    if (this == null) return null;

    final splitData = this!.split(':');

    if (splitData.length != 3) return null;

    final hours = int.tryParse(splitData[0]) ?? 0;
    final minutes = int.tryParse(splitData[1]) ?? 0;

    return TimeOfDay(hour: hours, minute: minutes);
  }

  String get hhmmToTime {
    if (this == null) return '';

    final splitData = this!.split(':');

    if (splitData.length != 3) return '';

    final hours = int.tryParse(splitData[0]) ?? 0;
    final minutes = int.tryParse(splitData[1]) ?? 0;
    final minutesString =
        minutes.toString().length == 1 ? '0$minutes' : minutes.toString();
    return '${hours}h$minutesString';
  }

  String? obscure(int start, [int? end]) {
    if (isNullOrEmpty || this!.length <= start) return this;

    var endValue = end ?? this!.length;

    final startValue = start < 0 || start >= this!.length ? 0 : start;

    if (endValue >= this!.length) endValue = this!.length;

    final characters = this!.characters.toList();

    for (var i = startValue; i < endValue; i++) {
      characters[i] = '*';
    }

    return characters.join();
  }

  String? obscureEmail(int start, [int? end]) {
    var startIndex = start;
    final endIndex = end ?? this?.indexOf('@');

    if (endIndex != null && start >= endIndex) {
      startIndex = endIndex - 1;
    }

    return obscure(startIndex, endIndex);
  }

  String? insertCharacterAt(int index, String character) {
    if (isNullOrEmpty || this!.length <= index || index < 0) return this;

    return this!.substring(0, index) + character + this!.substring(index);
  }

  Duration? parseDuration() {
    if (isNullOrEmpty) return null;

    final parts = this!.split(':');

    if (parts.length == 3) {
      final hours = int.parse(parts[0]);
      final minutes = int.parse(parts[1]);
      final seconds = int.parse(parts[2]);

      return Duration(hours: hours, minutes: minutes, seconds: seconds);
    }

    if (parts.length == 2) {
      final hours = int.parse(parts[0]);
      final minutes = int.parse(parts[1]);

      return Duration(hours: hours, minutes: minutes);
    }

    return null;
  }

  String truncateZero() {
    final regex = RegExp(r'([.]*0)(?!.*\d)');

    final value = (this ?? '').replaceAll(regex, '');
    return value;
  }
}

extension StringCasingExtension on String {
  String getInitials({int? limitTo}) {
    final buffer = StringBuffer();
    final split = this.split(' ');
    // for (var i = (split.length - (limitTo ?? split.length));
    //     i < split.length;
    //     i++) {
    //   buffer.write(split[i][0]);
    // }
    if (split.length >= 2) {
      buffer.write(split[split.length - 1][0]);
    }
    buffer.write(split[0][0]);
    return buffer.toString();
  }

  String toCapitalized() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';

  String toTitleCase() => replaceAll(RegExp(' +'), ' ')
      .split(' ')
      .map((str) => str.toCapitalized())
      .join(' ');

  String? get reverse => split('').reversed.join();

}
