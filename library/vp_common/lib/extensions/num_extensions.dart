import 'dart:math';

import 'package:intl/intl.dart';
import 'package:vp_common/vp_common.dart';

extension NumExtensions on num {
  DateTime? timestampToDateTime({
    bool isMilliseconds = true,
    bool isUtc = false,
  }) {
    var newTimestamp = toInt();
    if (!isMilliseconds) newTimestamp *= 1000;

    return DateTime.fromMillisecondsSinceEpoch(newTimestamp, isUtc: isUtc);
  }

  String toFormatThousandSeparator() {
    if (this == 0) return '';
    final format = NumberFormat('#,###.##', 'en_US');
    final result = format.format(this);
    return result.trim();
  }

  String toMoney({
    bool addCharacter = false,
    bool showSymbol = true,
    String? symbol,
  }) {
    final value = this == -0.0 ? 0 : this;

    final format = NumberFormat.currency(
      locale: 'vi',
      symbol: showSymbol ? (symbol ?? VPCommonLocalize.current.vnd) : '',
    );

    var result = format.format(value);
    result = result.trim().replaceAll('.', ',');

    if (addCharacter && value > 0 && !result.startsWith('+')) {
      result = '+$result';
    }

    return result;
  }

  String format() {
    final format = NumberFormat('#,###.##', 'en_US');
    final result = format.format(this);
    return result.trim();
  }

  String toFormat3() {
    final format = NumberFormat('###,###,###', 'en_US');
    final result = format.format(this);
    return result.trim();
  }

  String toFormat4() {
    final num val = double.parse(toStringAsFixed(1));
    if (val % 1 == 0) {
      final format = NumberFormat("###,###,##0.0", "en_US");
      final result = format.format(val);
      return result.trim();
    }
    final format = NumberFormat("###,###,###.#", "en_US");
    final result = format.format(val);
    return result.trim();
  }

  bool get isOddLot => this > 0.0 && this < 100;

  bool isZero() {
    return this == 0 || this == 0.0;
  }

  double toPrecision(int fractionDigits) {
    final mod = pow(10, fractionDigits.toDouble()).toDouble();
    return (this * mod).round().toDouble() / mod;
  }

  String getPriceFormatted({
    bool convertToThousand = false,
    String currency = '',
    bool trimZero = false,
    bool prefix = false,
  }) {
    if (this == 0) {
      return '0';
    }
    var value = this;
    var decimalDigits = 2;
    if (convertToThousand) {
      value = value / 1000;
    }

    if (trimZero) {
      if (value == value.round().toDouble()) {
        decimalDigits = 0;
      }
    }

    final format = NumberFormat.currency(
      locale: 'en-US',
      symbol: '',
      decimalDigits: decimalDigits,
    );

    final prefixText = prefix && value > 0.0 ? '+' : '';

    return '$prefixText${format.format(value)}$currency';
  }
   String getChangePercentDisplay(
      {bool addCharacter = false, int? fractionDigits}) {
    if (this == 0) {
      return '0%';
    }

    if (this == 100) {
      return '100%';
    }
    if (fractionDigits != null) {
      return '${(addCharacter && this > 0) ? '+' : ''}${toStringAsFixed(fractionDigits)}%';
    } else {
      if ((this * 100) % 10 == 0) {
        return '${(addCharacter && this > 0) ? '+' : ''}${toStringAsFixed(1)}%';
      }

      return '${(addCharacter && this > 0) ? '+' : ''}${toStringAsFixed(2)}%';
    }
  }

  String getMarketChangePercentDisplay({int fractionDigits = 2}) {
    if (this == 0) {
      return '0%';
    }

    if (this == 100) {
      return '100%';
    }

    if (this > 0) {
      return '${toStringAsFixed(fractionDigits)}%';
    }

    return '${toStringAsFixed(fractionDigits)}%';
  }

   String toPriceChart({NumberFormat? numberFormat}) {
    final currencyFormat =
        numberFormat ?? NumberFormat('#,###.#', 'en_ES');
    var volume = toDouble();
    if (volume >= 100000000) {
      volume = volume / 1000000000;
      return '${currencyFormat.format(volume)} tỷ';
    }
    if (volume >= 100000) {
      volume = volume / 1000000;
      return '${currencyFormat.format(volume)} tr';
    }
    if (volume >= 100) {
      volume = volume / 1000;
      return '${currencyFormat.format(volume)} nghìn';
    }
    return volume.toInt().toString();
  }

  String convertToABSValueAndFormat() {
    return abs().toDouble().toStringAsFixed(2);
  }
}
