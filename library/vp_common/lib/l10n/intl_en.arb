{"@@locale": "en", "account": "Account", "loading": "Loading, please wait", "all_sub_account": "All sub-account", "sub_account": "Sub-account", "k": "k", "mil": "mil", "bil": "billion", "tt": "TT", "tv": "TV", "ts": "TS", "vnd": "đ", "err_default": "Your connection to the system failed. Please try again.", "err_internet": "Please double check your internet connection", "err_unknown": "An error occurred. Please try again", "err_timeout": "Your connection is unstable. Please try again or contact support.", "no_data": "No data", "error": "Failed to retrieve data, please try again", "completed": "Completed", "tutorial_home1": "View Account Info, Setup Settings and Help", "tutorial_home2": "Place Stock Orders, Hold Portfolio, Order Book, Market Info, Watchlist and other stock widgets", "tutorial_home3": "Place derivative orders", "tutorial_home4": "Using Bonds, eInvest", "tutorial_home5": "View assets you are holding, Statistics of Money and operations Deposit, Transfer money", "tutorial_stock1": "Select your watch category, edit the category name, order the codes and remove the codes from the category", "tutorial_stock2": "Add stock ticker to your self-created portfolio", "tutorial_stock3": "Stock filter, Market information, Stock order confirmation, Profit and loss log, Cash advance and other utilities", "tutorial_assets1": "View details of Money items in the account", "tutorial_assets2": "See detailed value of the product portfolio you are holding", "tutorial_assets3": "View details of advances, deposits and fees", "tutorial_bond1": "Information about the Bond ticker, Place a Buy Order", "tutorial_bond2": "View referral orders to buy Bonds from Carers", "tutorial_order1": "To place a stock order, please enter order information and stock code", "tutorial_order2": "Market information of the selected token", "tutorial_order3": "Enter the price and volume of the selected ticker to complete the stock order", "next": "Next", "skip": "<PERSON><PERSON>", "cancel": "Cancel", "close": "Close", "subAccount_derivative": "Derivative sub-account", "subAccount_standard": "Standard sub-account", "subAccount_margin": "Margin sub-account", "subAccount_bond": "Bond sub-account", "subAccount_entrusted": "Entrusted sub-account", "subAccount_wealth": "Wealth sub-account", "all": "All", "save": "Save", "accept": "Accept", "date": "<PERSON><PERSON><PERSON>", "month": "th<PERSON>g", "year": "year", "apply": "<PERSON><PERSON>", "no_internet": "No network connection, please try again", "no_filter": "There are no results matching the filter. Please change the filter condition and try again.", "option": "Option", "retry": "Retry", "reset": "Reset", "search": "Search", "noDataMatching": "There are no matching results", "notGetData": "Not get data", "confirm": "Confirm", "somethingError": "Something error. Please try again", "understood": "Understood", "download_fail": "Download Fail", "download_success": "Download Success", "downloading": "Downloading", "back": "Back"}