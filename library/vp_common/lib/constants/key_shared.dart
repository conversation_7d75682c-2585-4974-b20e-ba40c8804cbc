class KeyShared {
  static String isFirst = 'is_first';
  static String theme = 'theme';
  static String accessToken = 'access_token';
  static String onboardingToken = 'onboarding_token';
  static String tokenType = 'tokenType';
  static String isProfession = 'isProfession';
  static String isBondServiceProfession = 'isBondServiceProfession';
  static String biometrics = 'biometrics';
  static String showConfirmOrderMessage = 'showConfirmOrderMessage';
  static String appColor = 'appColor';
  static String appLanguage = 'appLanguage';
  static String showIntroNewOrder = 'showIntroNewOrder';
  static String showNewOrder = 'showNewOrder';
  static String isBlockShowGameToday = 'isBlockShowGameToday';
  static const String watchlist = "watchlist";
  static const String listAccount = 'list_account';
  static const String defaultSubAccount = 'default_sub_account';
  static const String saveFingerprint = 'saveFingerprint';
  static const String needSignature = 'needSignature';

  static String getKeyBiometrics(String accNo) {
    return '${KeyShared.biometrics}_$accNo';
  }

  static const uuid = 'uuid';

  static const appDeviceID = 'appDeviceID';

  static String currentThemeApp = 'currentThemeApp';
  static String saveStatusSurveySuccess = 'saveStatusSurveySuccess';
  static String pointSurvey = 'pointSurvey';

  static String lastUpdateRatingDialog = 'lastUpdateRatingDialog';
  static String symbolFollowOrder = 'symbolFollowOrder';

  /// Favorist Feature
  static String listFavorist = 'listFavoristSelect';
  static String pointRankDataCoint = 'pointRankDataCoint';
  static const String defaultIsMarginSubAccount =
      'default_is_margin_sub_account';
}
