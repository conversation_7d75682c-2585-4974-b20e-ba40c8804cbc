const defaultDuration = Duration(milliseconds: 100);
const characterPIN = 4;
const maxLengthPIN = 30;
const minLengthPIN = 6;

const characterOTP = 6;
const smsOtpTimeOut = 180;

const smartOtpTimeOut = 30;
const smartPinLength = 4;

const closeInputOTPErrorCode = 321;
const smartOTPUnRegistered = 322;
const needUpdateCCCD12 = 'IABERRK1085';
const customerWaitApproval = 'IABERR451';

// IA.B.CEN.ERR.000=Lỗi xác thực bước 2
// IA.B.CEN.NOT.001=Cần xác thực bước 2
// IA.B.CEN.ERR.002=Xác thực bước 2 thất bại
// IA.B.CEN.NOT.003=Xác thực bước 2 thành công
// IA.B.CEN.ERR.004=Phương thức xác thực không chính xác
// IA.B.CEN.ERR.005=Phương thức xác thực chưa kích hoạt
const needVerify2FA = 'IA.B.CEN.NOT.001';
const errorWhileVerify2FA = 'IA.B.CEN.ERR.000';
const verify2FAFail = 'IA.B.CEN.ERR.002';
const otpExpired = 'IA.B.CEN.ERR.006';

const emailVerifiedInOtherDevice = 'IABERR228';
const fssVerify2FAFail = 'IABERR659';

const showErrorTextUnderInput = <String>[
  errorWhileVerify2FA,
  verify2FAFail,
  otpExpired
];

const closeCentralizeWhenCodeContains = <String>[
  '-700090',
  '-700091',
  '-700092',
  '-700093',
];
