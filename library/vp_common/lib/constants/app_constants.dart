class AppConstants {
  static const captchaEnterpriseAndroidKey =
      '6LeQIFYrAAAAACK80p3JtXux05a5l4smYLHrD4zM';
  static const captchaEnterpriseIosKey =
      '6LctQFYrAAAAAHMHP8cSm4Akg3ZHOfZ3MCG7C8sI';

  /// Language
  static String vi = 'vi';
  static String en = 'en';

  /// link store
  static const String app_store_link =
      'https://apps.apple.com/vn/app/vpbank-securities-neo-invest/id1629564549';
  static const String play_store_link =
      'https://play.google.com/store/apps/details?id=com.chungkhoanvpbank.mobiletrading';

  static const zalo = 'https://zalo.me/1388169892273543209';
  static const linkedIn = 'https://www.linkedin.com/company/chungkhoanvpbank/';
  static const messenger = 'https://www.facebook.com/ckvpbank';
  static const website = 'https://www.vpbanks.com.vn/';
  static const realtimeDatabase =
      'https://vpbanks-securities-trading-default-rtdb.asia-southeast1.firebasedatabase.app/';

  /// Số điểm giao dịch tối đa trong 1 ngày (không tính giờ nghỉ trưa)
  /// 4.5: Giờ chứng khoán giao dịch trong ngày
  static const maxPointInDay = 56;

  /// Số điểm giao dịch tối đa trong 1 ngày (Có tính giờ nghỉ trưa)
  /// 6: Giờ chứng khoán giao dịch trong ngày (tính cả nghỉ trưa)
  static const maxPointInDayNoBreak = 73;

  /// Mỗi 5 phút lấy 1 điểm
  static const frequent = 5;

  /// 12 digits and 3 ',' character
  static const maxLengthMoneyTransfer = 15;
  static const maxLengthAdvanceMoney = 15;

  /// sms otp timeout
  static const smsOtpTimeout = 180;

  static const maxLength = 254;

  /// https://jira.vpbanks.com.vn/browse/CKCS-1621
  static const smsOtpMoneyTransferTimeout = 121;

  /// https://jira.vpbanks.com.vn/browse/CKCS-1646
  static const smsOtpOnOffEInvest = 121;

  static const String bondNotRegistered = 'FO20010';
  static const String smartAndSmsOTPNotRegistered = 'FO20014';
  static const String systemMaintenance = 'SYS.001';

  static const int notiCoreOpenBrowser = 2;
  static const int notiCoreOpenBottomSheet = 3;
  static const int notiCoreNavigateInApp = 1;
  static const int notiCoreView = 4;

  ///Mã ứng dụng
  static const String appCode = 'NIA';

  /// screen code
  /// https://confluence.vpbanks.com.vn/pages/viewpage.action?pageId=********

  /// Danh sách cổ phiếu
  static const loyalty = 'loyalty';
  static const stockList = 'stock_list';
  static const stockOrder = 'stock_order';
  static const stockMarket = 'stock_market';
  static const stockPortfolio = 'stock_portfolio';
  static const stockOrderBook = 'stock_orderbook';
  static const stockCashAdvance = 'stock_cash_advance';
  static const stockTransfer = 'stock_transfer';
  static const stockRightsIssues = 'stock_rights_issues';
  static const stockConfirmOrder = 'stock_confirm_order';
  static const stockMarginList = 'stock_margin_list';
  static const stockProfitloss = 'stock_profitloss';
  static const stockRecommendation = 'stock_recommendation';
  static const bondPortfolio = 'bond_portfolio';
  static const bondList = 'bond_list';
  static const bondReferralOrder = 'bond_referral_order';
  static const bondHistory = 'bond_history';
  static const assetOverall = 'asset_overall';
  static const moneyOverall = 'money_overall';
  static const moneyEinvest = 'money_einvest';
  static const einvestRecommendation = 'einvest_recommendation';
  static const moneyTransfer = 'money_transfer';
  static const moneyHistory = 'money_history';
  static const ekycVpbank = 'ekyc_vpbank';
  static const referral = 'referral';
  static const financialServicePackage = 'stock_finance_service';
  static const bondContract = 'bond_contract';
  static const openAccountVietinBank = 'open_account_vietinbank';
  static const partnerConnection = 'partner_connection';
  static const eportfolio = 'eportfolio';
  static const fund = 'fund';
  static const stockScreener = 'stock_screener';

  static const copyTradeInvest = 'Copiers_Of_Account';
  static const copyTradeRightList = 'Right_List';

  ///bộ lọc thời gian
  static const String oneDay = '1D';
  static const String oneWeek = '1W';
  static const String oneMonth = '1M';
  static const String threeMonth = '3M';
  static const String oneYear = '1Y';
  static const String fiveDays = '5D';
  static const String fiveMinutes = '5m';
  static const String fifteenMinutes = '15m';
  static const String thirtyMinutes = '30m';
  static const String oneHours = '1h';
  static const String fourHours = '4h';

  ///loại biểu đồ
  static const String chartMarket = 'market';
  static const String chartStock = 'stock';

  ///hotline
  static const String hotline = '1900 636679';

  static const String support = '**********';

  ///status category notification
  static const String active = 'ACTIVE';
  static const String inactive = 'INACTIVE';

  static const String defaultChartTimeLine = '1D';

  ///sắp xếp
  static const String asc = 'ASC';
  static const String desc = 'DESC';

  /// https://confluence.vpbanks.com.vn/pages/viewpage.action?pageId=********#Danhm%E1%BB%A5cc%C3%A1cEnumtr%C3%AAnh%E1%BB%87th%E1%BB%91ng-K%C3%AAnh(via)
  static const viaApp = 'Y';

  static const String dmlsTPlus =
      'https://www.vpbanks.com.vn/post/vpbanks-ra-mat-san-pham-margin-moi';

  static const String dmls99 =
      'https://www.vpbanks.com.vn/post/chinh-sach-uu-dai-lai-suat-theo-danh-muc';

  static const String dmlsDebt =
      'https://www.vpbanks.com.vn/post/thong-tin-san-pham-bac-du-no';

  //loại báo cáo Model Portfolio
  static const String lb = 'LB';

  //ngành theo các cấp
  static const String icbLevel1 = 'icbLevel1';
  static const String icbLevel2 = 'icbLevel2';
  static const String derivativeAccountType = '.8';

  /// config banner tet
  static const int showBannerTet = 1;

  static const String defaultURL =
      'https://www.vpbank.com.vn/uu-dai?f=Ngan-hang-so';
  static const String urlChinhSach =
      'https://3666a9e7-62c9-4359-ae49-b288472335bc.usrfiles.com/ugd/3666a9_67ad4ee026a14bc2b7ba63ca08b7ee70.pdf';
  static const String derivativeContract =
      'https://33ebaeb4-b166-4507-af2e-e29a08919483.usrfiles.com/ugd/33ebae_61cc6e25448e4fe1bfe3eca243356e09.pdf';
}
